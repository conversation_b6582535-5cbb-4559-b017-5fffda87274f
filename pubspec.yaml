name: npemployee
description: "A new Flutter project."
publish_to: 'none'

version: 1.2.1+**********

environment:
  sdk: '>=3.2.4 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  dio: ^5.4.1
  shared_preferences: ^2.0.15
  build_runner: ^2.3.3
  package_info: ^2.0.2
  provider: ^6.0.5
  webview_flutter: ^4.7.0
  video_player: ^2.9.2
  convert: ^3.1.1
  crypto: ^3.0.2
  flutter_svg: ^2.0.5
  cupertino_icons: ^1.0.2
  url_launcher: ^6.3.0
  clipboard: ^0.1.3
  intl: ^0.19.0
  fluwx: 4.5.6
  flutter_bloc: ^8.1.6
  fluro: ^2.0.5
  flutter_screenutil: ^5.9.3
  permission_handler: ^11.3.1
  image_picker: ^1.0.0
  share_plus: ^7.2.2
  # better_player: ^0.0.84
  # better_player:
  #   git:
  #     url: 'https://github.com/dleurs/betterplayer.git'
  just_audio: ^0.9.42
  flutter_tts: ^4.1.0
  mmkv: ^2.0.1
  cached_network_image: ^3.4.1
  flutter_easyloading: ^3.0.5
  flutter_html: ^3.0.0-beta.2
  photo_view: ^0.15.0
  flutter_downloader: ^1.11.8
  path_provider: ^2.1.5
  device_info_plus: ^10.1.0
  disk_space_plus: ^0.2.4
  syncfusion_flutter_pdfviewer: ^27.2.4
  health: 10.0.0
  # daily_pedometer2: ^1.1.3
  flutter_keyboard_visibility: ^6.0.0
  daily_pedometer: ^0.0.13
  flutter_background_service: ^5.0.10
  flutter_local_notifications: ^18.0.1
  image_cropper: ^8.0.2
  tap_debouncer: ^2.2.0
  flutter_inappwebview: ^6.1.5
  tencent_cloud_chat_uikit:
    path: ../pubspec/npemployee/tencent_cloud_chat_uikit-4.0.1
  easy_refresh: ^3.4.0
  tencent_calls_uikit: ^2.7.2
  tencent_cloud_chat_customer_service_plugin: ^0.1.6
  flutter_markdown: ^0.6.23
  extended_text: ^14.2.0
  tencent_cloud_chat_push: ^8.3.6498+2
  lottie: ^3.2.0
  geolocator: ^13.0.2
  mobile_scanner: 5.2.3
  marqueer: ^2.0.1
  connectivity_plus: ^6.1.2
  image_gallery_saver: ^2.0.3
  quick_actions: ^1.1.0
  app_links: ^6.3.3
  umeng_common_sdk: ^1.2.8
  umeng_apm_sdk: ^2.3.2
  install_plugin: ^2.1.0
  calendar_date_picker2: ^1.1.8
  visibility_detector: ^0.3.3
  chewie: ^1.10.0
  wechat_assets_picker: ^9.5.0
  wechat_camera_picker: ^4.3.7
  qr_flutter: ^4.1.0
  qr_code_dart_scan: ^0.8.3
  loading_animation_widget: ^1.3.0
  flutter_slidable_plus_plus: ^0.1.0
  scroll_to_index: ^2.1.1
  desktop_drop: ^0.4.4
  lpinyin: ^2.0.3
  azlistview_all_platforms: ^2.1.2
  flutter_blue_plus: ^1.35.5
  diff_match_patch: ^0.4.1
  extended_text_field: ^16.0.2
  fc_native_video_thumbnail: ^0.16.1
  path: ^1.9.0
  file_picker: ^5.5.0
  universal_html: ^2.2.4

dependency_overrides:
  uuid: ^4.1.0
  image_picker: ^1.0.0
  permission_handler: ^11.3.1
  cached_network_image: ^3.4.1
  visibility_detector: ^0.3.3
  file: ^6.1.2
  http: ^0.13.1
  tencent_cloud_chat_sdk:
    path: ../pubspec/npemployee/tencent_cloud_chat_sdk-8.3.6498+1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  json_serializable: ^6.8.0
  flutter_native_splash: ^2.4.4


flutter:
  uses-material-design: true

  assets:
    - assets/svg/
    - assets/svg/login/
    - assets/svg/mine/
    - assets/svg/study/
    - assets/svg/xiaoxin/
    - assets/svg/mine/profile/
    - assets/svg/mine/personal_info/
    - assets/png/
    - assets/png/login/
    - assets/png/home/
    - assets/png/study/
    - assets/png/health/
    - assets/png/xiaoxin/
    - assets/png/download/
    - assets/png/function/
    - assets/png/function/watch_course_query/
    - assets/png/im/
    - assets/png/mine/
    - assets/png/mine/team_manager/
    - assets/png/mine/task/
    - assets/lottie/
    - assets/html/

  fonts:
    # - family: AlibabaPuHuiTi
    #   fonts:
    #     - asset: assets/fonts/Alibaba-PuHuiTi-Bold.ttf
    #     #   weight: 700
    #     # - asset: assets/fonts/Alibaba-PuHuiTi-Medium.ttf
    #     #   weight: 500
    #     # - asset: assets/fonts/Alibaba-PuHuiTi-Heavy.ttf
    #     #   weight: 800
    #     # - asset: assets/fonts/Alibaba-PuHuiTi-Regular.ttf
    #     #   weight: 400
    #     # - asset: assets/fonts/Alibaba-PuHuiTi-Light.ttf
    #     #   weight: 300

    - family: PingFang
      fonts:
        - asset: assets/fonts/PingFang-Bold.ttf
          weight: 700
        - asset: assets/fonts/PingFang-Bold.ttf
          weight: 600
        - asset: assets/fonts/PingFang-Medium.ttf
          weight: 500
        - asset: assets/fonts/PingFang-Regular.ttf
          weight: 400