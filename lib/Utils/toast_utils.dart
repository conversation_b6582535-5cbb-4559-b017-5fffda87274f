import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class ToastUtils extends WidgetsBindingObserver {
  static final ToastUtils _instance = ToastUtils._internal();

  AppLifecycleState _appLifecycleState = AppLifecycleState.resumed;

  ToastUtils._internal() {
    WidgetsBinding.instance.addObserver(this);
  }

  static void init() {
    _instance;
  }

  bool get isAppInForeground => _appLifecycleState == AppLifecycleState.resumed;

  static void show(String message, {Duration? duration}) {
    if (_instance.isAppInForeground) {
      ToastUtils.show(message, duration: duration);
    } else {
      debugPrint("Toast not shown (App in background): $message");
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _appLifecycleState = state;
  }

  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
  }
}
