import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/navigator_push_utils.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/provider/header_color_provider.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:provider/provider.dart';
import 'package:npemployee/service/bluetooth_service.dart';
import 'package:npemployee/service/location_service.dart';
import 'package:npemployee/service/step_count_service.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class WebUtils {
  static StreamSubscription? _scanResultsSubscription;
  static StreamSubscription<String>? _positionStreamSubscription;

  /// ---------------------------- 向web发送消息
  static void _sendToWeb(String javaScript, WebViewController controller) {
    controller.runJavaScript(javaScript);
  }

  /// ---------------------------- jschannel
  static void jsChannelHandler(
      JavaScriptMessage message,
      WebViewController controller,
      BuildContext context,
      void Function()? callback) async {
    final res = jsonDecode(message.message);
    String action = res['action'];
    Map data = res['data'] ?? {};
    debugPrint('-------- 收到 web 发送的 $action 消息');
    if (action == 'getLocation') {
      _getPosition(data, controller);
    } else if (action == 'exit') {
      NavigatorUtils.pop(context);
    } else if (action == 'openLocationSetting') {
      if (Platform.isAndroid) {
        LocationService().openLocationSettings();
      } else if (Platform.isIOS) {
        LocationService().openAppSettings();
      }
    } else if (action == 'pushXtjrAppPage') {
      List? jump = data['jump'];
      List? style = data['style'];
      NavigatorPushUtils.to(context, jump, style, () {
        callback?.call();
      });
    } else if (action == 'safeHeight') {
      _sendToWeb(
          'window.safeHeightCallback(${jsonEncode({
                "status": ScreenUtil().statusBarHeight,
                "bottom": ScreenUtil().bottomBarHeight
              })})',
          controller);
    } else if (action == "headerColorChange") {
      _headerColorChange(data, context);
    }
    // 蓝牙相关功能
    else if (action == 'isBluetoothSupported') {
      String result = await BluetoothService().isBluetoothSupported();
      _sendToWeb('window.bluetoothCallback("isBluetoothSupported", $result)',
          controller);
    } else if (action == 'getBluetoothState') {
      String result = await BluetoothService().getBluetoothState();
      _sendToWeb(
          'window.bluetoothCallback("getBluetoothState", $result)', controller);
    } else if (action == 'turnOnBluetooth') {
      String result = await BluetoothService().turnOnBluetooth();
      _sendToWeb(
          'window.bluetoothCallback("turnOnBluetooth", $result)', controller);
    } else if (action == 'startScan') {
      int? timeout = data['timeout'] != null
          ? int.tryParse(data['timeout'].toString())
          : null;
      List<String>? withServices = data['withServices'] != null
          ? List<String>.from(data['withServices'])
          : null;
      List<String>? withNames = data['withNames'] != null
          ? List<String>.from(data['withNames'])
          : null;

      String result = await BluetoothService().startScan(
        timeout: timeout,
        withServices: withServices,
        withNames: withNames,
      );
      _sendToWeb('window.bluetoothCallback("startScan", $result)', controller);
    } else if (action == 'stopScan') {
      String result = await BluetoothService().stopScan();
      _sendToWeb('window.bluetoothCallback("stopScan", $result)', controller);
    } else if (action == 'getScanResults') {
      String result = await BluetoothService().getScanResults();
      _sendToWeb(
          'window.bluetoothCallback("getScanResults", $result)', controller);
    } else if (action == 'connectToDevice') {
      String deviceId = data['deviceId'] ?? '';
      bool autoConnect = data['autoConnect'] == true;
      int? timeout = data['timeout'] != null
          ? int.tryParse(data['timeout'].toString())
          : null;

      String result = await BluetoothService().connectToDevice(
        deviceId,
        autoConnect: autoConnect,
        timeout: timeout,
      );
      _sendToWeb(
          'window.bluetoothCallback("connectToDevice", $result)', controller);
    } else if (action == 'disconnectDevice') {
      String result = await BluetoothService().disconnectDevice();
      _sendToWeb(
          'window.bluetoothCallback("disconnectDevice", $result)', controller);
    } else if (action == 'getConnectedDevice') {
      String result = await BluetoothService().getConnectedDevice();
      _sendToWeb('window.bluetoothCallback("getConnectedDevice", $result)',
          controller);
    } else if (action == 'discoverServices') {
      String result = await BluetoothService().discoverServices();
      _sendToWeb(
          'window.bluetoothCallback("discoverServices", $result)', controller);
    } else if (action == 'readCharacteristic') {
      String serviceUuid = data['serviceUuid'] ?? '';
      String characteristicUuid = data['characteristicUuid'] ?? '';

      String result = await BluetoothService().readCharacteristic(
        serviceUuid,
        characteristicUuid,
      );
      _sendToWeb('window.bluetoothCallback("readCharacteristic", $result)',
          controller);
    } else if (action == 'writeCharacteristic') {
      String serviceUuid = data['serviceUuid'] ?? '';
      String characteristicUuid = data['characteristicUuid'] ?? '';
      String value = data['value'] ?? '';
      bool withoutResponse = data['withoutResponse'] == true;

      String result = await BluetoothService().writeCharacteristic(
        serviceUuid,
        characteristicUuid,
        value,
        withoutResponse: withoutResponse,
      );
      _sendToWeb('window.bluetoothCallback("writeCharacteristic", $result)',
          controller);
    } else if (action == 'setNotification') {
      String serviceUuid = data['serviceUuid'] ?? '';
      String characteristicUuid = data['characteristicUuid'] ?? '';
      bool enable = data['enable'] == true;

      String result = await BluetoothService().setNotification(
        serviceUuid,
        characteristicUuid,
        enable,
      );
      _sendToWeb(
          'window.bluetoothCallback("setNotification", $result)', controller);
    } else if (action == 'getSystemDevices') {
      String result = await BluetoothService().getSystemDevices();
      _sendToWeb(
          'window.bluetoothCallback("getSystemDevices", $result)', controller);
    } else if (action == 'scanResultsListen') {
      // 监听蓝牙扫描结果
      _scanResultsSubscription =
          BluetoothService().scanResultsStream.listen((result) {
        if (context.mounted) {
          _sendToWeb('window.bluetoothCallback("scanResultsUpdate", $result)',
              controller);
        }
      });
    } else if (action == 'bluetoothScanCancel') {
      _scanResultsSubscription?.cancel();
    }
    // 定位相关功能
    else if (action == 'isLocationServiceEnabled') {
      String result = await LocationService().isLocationServiceEnabledApi();
      _sendToWeb('window.locationCallback("isLocationServiceEnabled", $result)',
          controller);
    } else if (action == 'checkPermission') {
      String result = await LocationService().checkPermissionApi();
      _sendToWeb(
          'window.locationCallback("checkPermission", $result)', controller);
    } else if (action == 'requestPermission') {
      String result = await LocationService().requestPermissionApi();
      _sendToWeb(
          'window.locationCallback("requestPermission", $result)', controller);
    } else if (action == 'getCurrentPosition') {
      String? accuracy = data['accuracy'];
      int? timeLimit = data['timeLimit'] != null
          ? int.tryParse(data['timeLimit'].toString())
          : null;

      String result = await LocationService().getCurrentPositionApi(
        accuracy: accuracy,
        timeLimit: timeLimit,
      );
      _sendToWeb(
          'window.locationCallback("getCurrentPosition", $result)', controller);
    } else if (action == 'getLastKnownPosition') {
      String result = await LocationService().getLastKnownPositionApi();
      _sendToWeb('window.locationCallback("getLastKnownPosition", $result)',
          controller);
    } else if (action == 'getLocationAccuracy') {
      String result = await LocationService().getLocationAccuracyApi();
      _sendToWeb('window.locationCallback("getLocationAccuracy", $result)',
          controller);
    } else if (action == 'calculateDistance') {
      double? startLatitude = data['startLatitude'] != null
          ? double.tryParse(data['startLatitude'].toString())
          : null;
      double? startLongitude = data['startLongitude'] != null
          ? double.tryParse(data['startLongitude'].toString())
          : null;
      double? endLatitude = data['endLatitude'] != null
          ? double.tryParse(data['endLatitude'].toString())
          : null;
      double? endLongitude = data['endLongitude'] != null
          ? double.tryParse(data['endLongitude'].toString())
          : null;

      if (startLatitude == null ||
          startLongitude == null ||
          endLatitude == null ||
          endLongitude == null) {
        String errorResult =
            jsonEncode({'code': -1, 'data': 0.0, 'msg': '坐标参数无效'});
        controller.runJavaScript(
            'window.locationCallback("calculateDistance", $errorResult)');
        return;
      }

      String result = await LocationService().calculateDistanceApi(
        startLatitude: startLatitude,
        startLongitude: startLongitude,
        endLatitude: endLatitude,
        endLongitude: endLongitude,
      );
      _sendToWeb(
          'window.locationCallback("calculateDistance", $result)', controller);
    } else if (action == 'startPositionStream') {
      String? accuracy = data['accuracy'];
      int? distanceFilter = data['distanceFilter'] != null
          ? int.tryParse(data['distanceFilter'].toString())
          : null;
      int? timeInterval = data['timeInterval'] != null
          ? int.tryParse(data['timeInterval'].toString())
          : null;

      String result = await LocationService().startPositionStreamApi(
        accuracy: accuracy,
        distanceFilter: distanceFilter,
        timeInterval: timeInterval,
      );
      _sendToWeb('window.locationCallback("startPositionStream", $result)',
          controller);
    } else if (action == 'stopPositionStream') {
      String result = await LocationService().stopPositionStreamApi();
      _sendToWeb(
          'window.locationCallback("stopPositionStream", $result)', controller);
    } else if (action == 'openAppSettings') {
      String result = await LocationService().openAppSettingsApi();
      _sendToWeb(
          'window.locationCallback("openAppSettings", $result)', controller);
    } else if (action == 'openLocationSettings') {
      String result = await LocationService().openLocationSettingsApi();
      _sendToWeb('window.locationCallback("openLocationSettings", $result)',
          controller);
    } else if (action == 'positionStreamListen') {
      // 监听位置更新
      _positionStreamSubscription =
          LocationService().positionStream.listen((result) {
        if (context.mounted) {
          controller.runJavaScript(
              'window.locationCallback("positionUpdate", $result)');
        }
      });
    } else if (action == 'positionStreamCancel') {
      _positionStreamSubscription?.cancel();
    }
    // 步数相关功能
    else if (action == 'checkStepPermission') {
      String result = await StepCountService().checkStepPermissionApi();
      _sendToWeb('window.stepCountCallback("checkStepPermission", $result)',
          controller);
    } else if (action == 'requestStepPermission') {
      String result = await StepCountService().requestStepPermissionApi();
      _sendToWeb('window.stepCountCallback("requestStepPermission", $result)',
          controller);
    } else if (action == 'getTodayStepCount') {
      String result = await StepCountService().getTodayStepCountApi();
      _sendToWeb(
          'window.stepCountCallback("getTodayStepCount", $result)', controller);
    } else if (action == 'getStepCountByDate') {
      String? dateString = data['date'];
      if (dateString == null || dateString.isEmpty) {
        String errorResult =
            jsonEncode({'code': -1, 'data': 0, 'msg': '日期参数无效'});
        _sendToWeb(
            'window.stepCountCallback("getStepCountByDate", $errorResult)',
            controller);
        return;
      }

      try {
        DateTime date = DateTime.parse(dateString);
        String result = await StepCountService().getStepCountByDateApi(date);
        _sendToWeb('window.stepCountCallback("getStepCountByDate", $result)',
            controller);
      } catch (e) {
        String errorResult =
            jsonEncode({'code': -1, 'data': 0, 'msg': '日期格式错误: $e'});
        _sendToWeb(
            'window.stepCountCallback("getStepCountByDate", $errorResult)',
            controller);
      }
    } else if (action == 'startStepCountStream') {
      String result = await StepCountService().startStepCountStreamApi();
      _sendToWeb('window.stepCountCallback("startStepCountStream", $result)',
          controller);
    } else if (action == 'stopStepCountStream') {
      String result = await StepCountService().stopStepCountStreamApi();
      _sendToWeb('window.stepCountCallback("stopStepCountStream", $result)',
          controller);
    } else if (action == 'openAppSettings') {
      String result = await StepCountService().openAppSettingsApi();
      _sendToWeb(
          'window.stepCountCallback("openAppSettings", $result)', controller);
    }
  }

  /// ---------------------------- 改变App状态栏颜色
  static void _headerColorChange(Map data, BuildContext context) {
    try {
      String? headerColor = data['headerColor'];
      if (headerColor != null && headerColor.isNotEmpty) {
        GlobalPreferences().webHeaderColor = headerColor;
        Color statusBarColor = headerColor.toColor();

        // 使用Provider更新颜色状态
        final headerColorProvider =
            Provider.of<HeaderColorProvider>(context, listen: false);
        headerColorProvider.setHeaderColor(statusBarColor);

        // 设置状态栏颜色
        Brightness iconBrightness = _getIconBrightness(statusBarColor);
        ValidatorUtils.setStatusBarColor(statusBarColor, iconBrightness);
        debugPrint('全局状态栏颜色已更改为: $headerColor');
      }
    } catch (e) {
      debugPrint('解析状态栏颜色失败: $e');
    }
  }

  /// 根据颜色亮度决定状态栏图标颜色
  static Brightness _getIconBrightness(Color color) {
    // 计算颜色亮度
    double luminance = color.computeLuminance();
    return luminance > 0.5 ? Brightness.dark : Brightness.light;
  }

  /// ---------------------------- 定位
  static void _getPosition(Map data, WebViewController controller) {
    LocationService()
        .getPosition(
            timeOut: data['timeOut'],
            locationAccuracy: data['locationAccuracy'],
            distanceFilter: data['distanceFilter'])
        .then((value) {
      controller.runJavaScript('getPositionHandle($value)');
    });
  }

  /// ---------------------------- 保存图片
  static void saveImage(String imgUrl) async {
    PermissionState state = await PhotoManager.requestPermissionExtend();
    if (state.isAuth) {
      try {
        final imageData = await NetworkAssetBundle(Uri.parse(imgUrl)).load("");
        final bytes = imageData.buffer.asUint8List();
        final result = await ImageGallerySaver.saveImage(bytes);
        if (result['isSuccess']) {
          ToastUtils.show('图片保存成功');
        } else {
          ToastUtils.show('图片保存失败');
        }
      } catch (e) {
        print(e);
        ToastUtils.show('图片保存失败: $e');
      }
    } else {
      debugPrint("error: 请求相册权限失败");
    }
  }
}
