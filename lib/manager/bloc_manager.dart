import 'package:npemployee/page/login/login_bloc.dart';
import 'package:npemployee/provider/audio_bloc/audio_bloc.dart';
import 'package:npemployee/provider/avatar_bloc/avatar_bloc.dart';
import 'package:npemployee/provider/home_color_bloc/home_color_bloc.dart';
import 'package:npemployee/provider/tab_bloc/tab_bloc.dart';

class BlocManager {
  static final BlocManager _instance = BlocManager._internal();
  late final TabBloc tabBloc;
  late final LoginBloc loginBloc;
  late final AudioBloc audioBloc;
  late final AvatarBloc avatarBloc;
  late final HomeColorBloc homeColorBloc;

  factory BlocManager() {
    return _instance;
  }

  BlocManager._internal() {
    tabBloc = TabBloc();
    loginBloc = LoginBloc();
    audioBloc = AudioBloc();
    avatarBloc = AvatarBloc();
    homeColorBloc = HomeColorBloc();
  }
}
