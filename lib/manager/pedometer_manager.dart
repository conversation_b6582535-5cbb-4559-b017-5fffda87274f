import 'dart:io';
import 'dart:ui';

import 'package:daily_pedometer/daily_pedometer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class PedometerManager {
  static final PedometerManager _instance = PedometerManager._internal();

  factory PedometerManager() {
    return _instance;
  }

  PedometerManager._internal() {
    initializeService();
  }

  int _stepCount = 0;

  int get stepCount => _stepCount;

  set stepCount(int value) {
    _stepCount = value;
  }
}

const notificationChannelId = 'my_foreground';
Future<void> initializeService() async {
  if (Platform.isAndroid) {
    debugPrint("********************* 初始化计步器服务");
    final service = FlutterBackgroundService();
    service.invoke("refreshSensorListener");

    /// OPTIONAL, using custom notification channel id
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      notificationChannelId, // id
      'MY FOREGROUND SERVICE', // title
      description:
          'This channel is used for important notifications.', // description
      importance: Importance.low, // importance must be at low or higher level
    );

    final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
        FlutterLocalNotificationsPlugin();

    await flutterLocalNotificationsPlugin.initialize(
      const InitializationSettings(
          android: AndroidInitializationSettings('ic_bg_service_small')),
    );

    await flutterLocalNotificationsPlugin
        .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    await service.configure(
      androidConfiguration: AndroidConfiguration(
          onStart: onStart,
          autoStart: true,
          isForegroundMode: true,
          notificationChannelId: notificationChannelId,
          initialNotificationTitle: '运动健康服务',
          initialNotificationContent: '计步器服务初始化',
          foregroundServiceNotificationId: 888,
          foregroundServiceTypes: [AndroidForegroundType.dataSync]),
      iosConfiguration: IosConfiguration(),
    );
  }
}

@pragma('vm:entry-point')
void onStart(ServiceInstance service) async {
  DartPluginRegistrant.ensureInitialized();
  print("111111 : 11");
  final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  if (service is AndroidServiceInstance) {
    service.on('setAsForeground').listen((event) {
      print("111111 : 1");
      service.setAsForegroundService();
    });

    service.on('setAsBackground').listen((event) {
      print("111111 : 2");
      service.setAsBackgroundService();
    });
  }

  service.on('stopService').listen((event) {
    print("111111 : 3");
    service.stopSelf();
  });

  DailyPedometer pedometer = DailyPedometer();
  service.on("refreshSensorListener").listen((event) async {
    print("refreshSensorListener");
    await pedometer.refreshSensorListener();
  });

  if (service is AndroidServiceInstance) {
    await pedometer.initialize(true);
    print("DailyPedometer foregrondService ${pedometer.steps}");

    flutterLocalNotificationsPlugin.show(
      888,
      '运动健康',
      '今日步数 : ${pedometer.steps}',
      const NotificationDetails(
        android: AndroidNotificationDetails(
            notificationChannelId, notificationChannelId,
            icon: 'ic_bg_service_small', ongoing: true),
      ),
    );

    pedometer.dailyStepCountStream.listen((event) async {
      print("DailyPedometer foregrondService $event");
      flutterLocalNotificationsPlugin.show(
        888,
        '运动健康',
        '今日步数 : $event',
        const NotificationDetails(
          android: AndroidNotificationDetails(
              notificationChannelId, notificationChannelId,
              icon: 'ic_bg_service_small', ongoing: true),
        ),
      );
    });

    // pedometer.setMode(true);
    // pedometer.stepCountStream.listen((event) async {
    //   service.setForegroundNotificationInfo(
    //     title: 'test steps',
    //     content: 'today steps : $event',
    //   );
    // });
  }
}
