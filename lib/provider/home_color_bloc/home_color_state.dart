import 'package:flutter/material.dart';
import 'package:npemployee/common/app_theme.dart';

/// Home页面颜色状态
class HomeColorState {
  final Color statusBarColor;
  final Color textColor;
  final Brightness iconBrightness;
  final bool isDefault;
  
  const HomeColorState({
    required this.statusBarColor,
    required this.textColor,
    required this.iconBrightness,
    this.isDefault = true,
  });
  
  /// 默认Home页面颜色状态
  static const HomeColorState defaultState = HomeColorState(
    statusBarColor: Colors.transparent,
    textColor: AppTheme.colorBlackTitle,
    iconBrightness: Brightness.dark,
    isDefault: true,
  );
  
  /// 复制状态并修改部分属性
  HomeColorState copyWith({
    Color? statusBarColor,
    Color? textColor,
    Brightness? iconBrightness,
    bool? isDefault,
  }) {
    return HomeColorState(
      statusBarColor: statusBarColor ?? this.statusBarColor,
      textColor: textColor ?? this.textColor,
      iconBrightness: iconBrightness ?? this.iconBrightness,
      isDefault: isDefault ?? this.isDefault,
    );
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HomeColorState &&
        other.statusBarColor == statusBarColor &&
        other.textColor == textColor &&
        other.iconBrightness == iconBrightness &&
        other.isDefault == isDefault;
  }
  
  @override
  int get hashCode {
    return statusBarColor.hashCode ^ 
           textColor.hashCode ^ 
           iconBrightness.hashCode ^ 
           isDefault.hashCode;
  }
}
