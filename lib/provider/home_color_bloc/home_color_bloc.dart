import 'dart:async';
import 'package:flutter/material.dart';
import 'package:npemployee/provider/home_color_bloc/home_color_event.dart';
import 'package:npemployee/provider/home_color_bloc/home_color_state.dart';

/// Home页面颜色管理BLoC
class HomeColorBloc {
  final StreamController<HomeColorState> _stateController = StreamController<HomeColorState>.broadcast();
  final StreamController<HomeColorEvent> _eventController = StreamController<HomeColorEvent>();
  
  HomeColorState _currentState = HomeColorState.defaultState;
  
  /// 状态流
  Stream<HomeColorState> get stream => _stateController.stream;
  
  /// 当前状态
  HomeColorState get state => _currentState;
  
  HomeColorBloc() {
    _eventController.stream.listen(_handleEvent);
  }
  
  /// 添加事件
  void add(HomeColorEvent event) {
    _eventController.add(event);
  }
  
  /// 处理事件
  void _handleEvent(HomeColorEvent event) {
    if (event is ChangeHomeColorEvent) {
      _handleChangeHomeColor(event);
    } else if (event is ResetHomeColorEvent) {
      _handleResetHomeColor();
    }
  }
  
  /// 处理Home页面颜色变更
  void _handleChangeHomeColor(ChangeHomeColorEvent event) {
    final newState = HomeColorState(
      statusBarColor: event.statusBarColor,
      textColor: event.textColor,
      iconBrightness: event.iconBrightness,
      isDefault: false,
    );
    
    _updateState(newState);
  }
  
  /// 处理重置Home页面颜色
  void _handleResetHomeColor() {
    _updateState(HomeColorState.defaultState);
  }
  
  /// 更新状态
  void _updateState(HomeColorState newState) {
    if (_currentState != newState) {
      _currentState = newState;
      _stateController.add(_currentState);
    }
  }
  
  /// 释放资源
  void dispose() {
    _stateController.close();
    _eventController.close();
  }
}
