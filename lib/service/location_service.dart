import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:geolocator/geolocator.dart';

///一个封装了Geolocator插件功能的单例服务类。
///WGS84坐标系
///
///此类提供以下方法：
///-检查位置服务是否启用
///-检查并请求位置权限
///-获取当前和最后已知的设备位置
///-计算坐标之间的距离
///-获得位置精度
///-打开设备设置

class LocationService {
  final String _logTag = "LocationService"; // 特殊的日志标识符

  static final LocationService _instance = LocationService._internal();

  factory LocationService() {
    return _instance;
  }

  LocationService._internal() {
    _init();
  }

  // 位置流订阅
  StreamSubscription<Position>? _positionStreamSubscription;

  // 位置流控制器
  final StreamController<String> _positionStreamController =
      StreamController<String>.broadcast();

  // 位置流
  Stream<String> get positionStream => _positionStreamController.stream;

  // 初始化
  Future<void> _init() async {
    debugPrint('$_logTag 初始化定位服务');
  }

  ///检查设备上是否启用了位置服务。
  ///
  ///如果启用了位置服务，则返回“true”，否则返回“false”。
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  ///检查位置的当前权限状态。
  ///
  ///返回一个[LocationPermission]值，指示当前权限状态。
  Future<LocationPermission> checkPermission() async {
    return await Geolocator.checkPermission();
  }

  ///请求访问设备位置的权限。
  ///
  ///返回一个[LocationPermission]值，指示请求后的权限状态。
  Future<LocationPermission> requestPermission() async {
    return await Geolocator.requestPermission();
  }

  ///获取设备的当前位置。
  ///
  ///参数：
  ///-[精度]：位置数据的精度（默认值：高）
  ///-[timeLimit]：等待仓位的最长时间（默认：无限制）
  ///-[forceAndroidLocationManager]：是否使用Android的LocationManager（默认值：false）
  ///
  ///返回一个[Position]对象，其中包含设备的当前位置。
  Future<Position> getCurrentPosition({
    LocationAccuracy accuracy = LocationAccuracy.high,
    Duration? timeLimit,
    bool forceAndroidLocationManager = false,
  }) async {
    final LocationSettings locationSettings = LocationSettings(
      accuracy: accuracy,
      timeLimit: timeLimit,
    );

    return await Geolocator.getCurrentPosition(
      locationSettings: locationSettings,
    );
  }

  ///获取设备上存储的最后一个已知位置。
  ///
  ///返回一个[Position]对象，其中包含最后一个已知位置，如果没有可用位置，则返回“null”。
  Future<Position?> getLastKnownPosition() async {
    return await Geolocator.getLastKnownPosition();
  }

  ///计算两个坐标之间的距离，单位为米。
  ///
  ///参数：
  ///-[startLatitude]：起始位置的纬度
  ///-[startLongitude]：起始位置的经度
  ///-[endLatitude]：结束位置的纬度
  ///-[endLongitude]：终点位置的经度
  ///
  ///返回两个坐标之间的距离（单位为米）。
  double calculateDistance({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) {
    return Geolocator.distanceBetween(
      startLatitude,
      startLongitude,
      endLatitude,
      endLongitude,
    );
  }

  ///获取位置精度状态。（仅限Android和iOS 14+）
  ///返回一个[LocationAccuracyStatus]，指示用户是启用了精确精度还是降低了精度。
  /*
    Android：
      lowest	PRIORITY_PASSIVE	确保不会使用额外的电源来推导出位置。这强制要求请求将充当被动监听器，仅接收代表其他客户计算的“免费”位置，不会仅代表此请求计算任何位置。
      low	PRIORITY_LOW_POWER	请求有利于低功耗的权衡，但可能会牺牲位置准确性。
      medium	PRIORITY_BALANCED_POWER_ACCURACY	请求在位置准确性和功耗之间取得平衡的权衡。
      high+	PRIORITY_HIGH_ACCURACY	请求一种有利于高度准确位置的权衡，但可能以额外功耗为代价。
    iOS：
      lowest	kCLLocationAccuracyThreeKilometers	精确到最近的三公里。
      low	kCLLocationAccuracyKilometer	精确到最近的公里。
      medium	kCLLocationAccuracyHundredMeters	精确到一百米以内。
      high	kCLLocationAccuracyNearestTenMeters	精确到距离所需目标十米以内。
      best	kCLLocationAccuracyBest	可用的最佳准确度。
      bestForNavigation	kCLLocationAccuracyBestForNavigation	使用额外的传感器数据来促进导航应用程序的最高精度。
  */
  Future<LocationAccuracyStatus> getLocationAccuracy() async {
    return await Geolocator.getLocationAccuracy();
  }

  ///打开应用程序设置页面。
  ///在某些情况下，有必要询问用户并更新他们的设备设置。例如，当用户最初永久拒绝访问设备位置的权限时，或者如果位置服务未启用（并且，在Android上，自动解析不起作用）。在这些情况下，您可以使用openAppSettings或openLocationSettings方法将用户立即重定向到设备的设置页面。
  ///在Android上，openAppSettings方法会将用户重定向到应用程序特定设置，用户可以在那里更新必要的权限。openLocationSettings方法会将用户重定向到位置设置，用户可以在其中启用/禁用位置服务。
  ///在iOS上，我们不允许打开特定的设置页面，因此这两种方法都会将用户重定向到“设置”应用程序，用户可以从那里导航到正确的设置类别来更新权限或启用/禁用位置服务。
  ///如果设置页面已成功打开，则返回“true”，否则返回“false”。
  Future<bool> openAppSettings() async {
    return await Geolocator.openAppSettings();
  }

  ///打开位置设置页面。
  ///
  ///如果设置页面已成功打开，则返回“true”，否则返回“false”。
  Future<bool> openLocationSettings() async {
    return await Geolocator.openLocationSettings();
  }

  ///获取位置更新流。
  ///
  ///参数：
  ///-[精度]：位置数据的精度（默认值：高）
  ///-[distanceFilter]：更新前设备必须移动的最小距离（单位：米）（默认值：0）
  ///-[timeInterval]：位置更新之间的时间间隔（仅限Android，默认值：5000ms）
  ///-[timeLimit]：等待位置更新的最长时间（默认值：无限制）
  ///
  ///返回一个发出位置更新的[Stream＜Position＞]
  Stream<Position> getPositionStream({
    LocationAccuracy accuracy = LocationAccuracy.high,
    int distanceFilter = 0,
    int timeInterval = 5000,
    Duration? timeLimit,
  }) {
    final LocationSettings locationSettings = LocationSettings(
      accuracy: accuracy,
      distanceFilter: distanceFilter,
      timeLimit: timeLimit,
    );

    return Geolocator.getPositionStream(
      locationSettings: locationSettings,
    );
  }

  ///-[timeOut]：设备定位超时时，默认0
  ///-[locationAccuracy]：设备定位精度(lowest, low, medium, high)， 默认medium
  ///-[distanceFilter]：设备必须移动的最小距离（以米为单位），当您希望收到所有移动的通知时，请提供0。默认值为0
  /// ErrorCode
  ///  10010-设备未打开定位开关
  ///  10011-设备定位权限被拒绝
  ///  10012-设备定位权限被永久拒绝
  ///  10013-设备定位超时
  ///  10014-设备定位失败

  Future<String> getPosition(
      {int? timeOut, String? locationAccuracy, int? distanceFilter}) async {
    LocationAccuracy accuracy = _getAccuracy(locationAccuracy);
    try {
      bool locationServiceEnabled = await isLocationServiceEnabled();
      if (!locationServiceEnabled) {
        return jsonEncode({'code': 10010, 'msg': '定位服务未开启'});
      }

      LocationPermission permission = await checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await requestPermission();
        if (permission == LocationPermission.denied) {
          return jsonEncode({'code': 10011, 'msg': '定位权限被拒绝'});
        }
      }
      if (permission == LocationPermission.deniedForever) {
        return jsonEncode({'code': 10012, 'msg': '定位权限被永久拒绝'});
      }
      EasyLoading.show();
      LocationSettings locationSettings = Platform.isAndroid
          ? AndroidSettings(
              timeLimit: Duration(seconds: timeOut ?? 0),
              accuracy: accuracy,
              distanceFilter: distanceFilter ?? 0,
              forceLocationManager: true,
            )
          : AppleSettings(
              timeLimit: Duration(seconds: timeOut ?? 0),
              accuracy: accuracy,
              distanceFilter: distanceFilter ?? 0,
              activityType: ActivityType.fitness,
              pauseLocationUpdatesAutomatically: true);
      Position position = await Geolocator.getCurrentPosition(
          locationSettings: locationSettings);
      EasyLoading.dismiss();
      return jsonEncode({'code': 0, 'data': position.toJson()});
    } on TimeoutException catch (e) {
      EasyLoading.dismiss();
      debugPrint('$_logTag定位超时: $e');
      return jsonEncode({'code': 10013, 'msg': '定位超时'});
    } on Exception catch (e) {
      EasyLoading.dismiss();
      debugPrint('$_logTag定位失败: $e');
      return jsonEncode({'code': 10014, 'msg': '定位失败'});
    }
  }

  LocationAccuracy _getAccuracy(String? locationAccuracy) {
    switch (locationAccuracy) {
      case "lowest":
        return LocationAccuracy.lowest;
      case "low":
        return LocationAccuracy.low;
      case "medium":
        return LocationAccuracy.medium;
      case "high":
        return LocationAccuracy.high;
      default:
        return LocationAccuracy.medium;
    }
  }

  // ==================== WebView API 方法 ====================

  /// 检查定位服务是否启用
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> isLocationServiceEnabledApi() async {
    try {
      bool isEnabled = await isLocationServiceEnabled();
      return jsonEncode({
        'code': 0,
        'data': isEnabled,
        'msg': isEnabled ? '定位服务已启用' : '定位服务未启用'
      });
    } catch (e) {
      debugPrint('$_logTag 检查定位服务失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '检查定位服务失败: $e'});
    }
  }

  /// 检查定位权限
  /// 返回JSON格式: {"code": 0, "data": "permission_status", "msg": ""}
  Future<String> checkPermissionApi() async {
    try {
      LocationPermission permission = await checkPermission();
      String permissionStr = permission.toString().split('.').last;
      return jsonEncode(
          {'code': 0, 'data': permissionStr, 'msg': '定位权限状态: $permissionStr'});
    } catch (e) {
      debugPrint('$_logTag 检查定位权限失败: $e');
      return jsonEncode({'code': -1, 'data': 'unknown', 'msg': '检查定位权限失败: $e'});
    }
  }

  /// 请求定位权限
  /// 返回JSON格式: {"code": 0, "data": "permission_status", "msg": ""}
  Future<String> requestPermissionApi() async {
    try {
      LocationPermission permission = await requestPermission();
      String permissionStr = permission.toString().split('.').last;
      return jsonEncode(
          {'code': 0, 'data': permissionStr, 'msg': '权限请求结果: $permissionStr'});
    } catch (e) {
      debugPrint('$_logTag 请求定位权限失败: $e');
      return jsonEncode({'code': -1, 'data': 'unknown', 'msg': '请求定位权限失败: $e'});
    }
  }

  /// 获取当前位置
  /// 参数:
  /// - accuracy: 定位精度 (lowest, low, medium, high)
  /// - timeLimit: 超时时间（秒）
  /// 返回JSON格式: {"code": 0, "data": Position, "msg": ""}
  Future<String> getCurrentPositionApi({
    String? accuracy,
    int? timeLimit,
  }) async {
    try {
      // 检查定位服务
      if (!await isLocationServiceEnabled()) {
        return jsonEncode({'code': 10010, 'data': null, 'msg': '定位服务未开启'});
      }

      // 检查权限
      LocationPermission permission = await checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await requestPermission();
        if (permission == LocationPermission.denied) {
          return jsonEncode({'code': 10011, 'data': null, 'msg': '定位权限被拒绝'});
        }
      }
      if (permission == LocationPermission.deniedForever) {
        return jsonEncode({'code': 10012, 'data': null, 'msg': '定位权限被永久拒绝'});
      }

      // 显示加载提示
      EasyLoading.show(status: '正在获取位置...');

      LocationAccuracy locationAccuracy = _getAccuracy(accuracy);
      Duration? timeLimitDuration =
          timeLimit != null ? Duration(seconds: timeLimit) : null;

      Position position = await getCurrentPosition(
        accuracy: locationAccuracy,
        timeLimit: timeLimitDuration,
      );

      EasyLoading.dismiss();

      return jsonEncode(
          {'code': 0, 'data': position.toJson(), 'msg': '获取当前位置成功'});
    } on TimeoutException catch (e) {
      EasyLoading.dismiss();
      debugPrint('$_logTag 获取当前位置超时: $e');
      return jsonEncode({'code': 10013, 'data': null, 'msg': '定位超时'});
    } catch (e) {
      EasyLoading.dismiss();
      debugPrint('$_logTag 获取当前位置失败: $e');
      return jsonEncode({'code': 10014, 'data': null, 'msg': '获取当前位置失败: $e'});
    }
  }

  /// 获取最后已知位置
  /// 返回JSON格式: {"code": 0, "data": Position, "msg": ""}
  Future<String> getLastKnownPositionApi() async {
    try {
      Position? position = await getLastKnownPosition();
      if (position != null) {
        return jsonEncode(
            {'code': 0, 'data': position.toJson(), 'msg': '获取最后已知位置成功'});
      } else {
        return jsonEncode({'code': -1, 'data': null, 'msg': '没有最后已知位置'});
      }
    } catch (e) {
      debugPrint('$_logTag 获取最后已知位置失败: $e');
      return jsonEncode({'code': -1, 'data': null, 'msg': '获取最后已知位置失败: $e'});
    }
  }

  /// 获取定位精度状态
  /// 返回JSON格式: {"code": 0, "data": "accuracy_status", "msg": ""}
  Future<String> getLocationAccuracyApi() async {
    try {
      LocationAccuracyStatus accuracy = await getLocationAccuracy();
      String accuracyStr = accuracy.toString().split('.').last;
      return jsonEncode(
          {'code': 0, 'data': accuracyStr, 'msg': '定位精度状态: $accuracyStr'});
    } catch (e) {
      debugPrint('$_logTag 获取定位精度失败: $e');
      return jsonEncode({'code': -1, 'data': 'unknown', 'msg': '获取定位精度失败: $e'});
    }
  }

  /// 计算两点间距离
  /// 参数:
  /// - startLatitude: 起始纬度
  /// - startLongitude: 起始经度
  /// - endLatitude: 结束纬度
  /// - endLongitude: 结束经度
  /// 返回JSON格式: {"code": 0, "data": distance_in_meters, "msg": ""}
  Future<String> calculateDistanceApi({
    required double startLatitude,
    required double startLongitude,
    required double endLatitude,
    required double endLongitude,
  }) async {
    try {
      double distance = calculateDistance(
        startLatitude: startLatitude,
        startLongitude: startLongitude,
        endLatitude: endLatitude,
        endLongitude: endLongitude,
      );

      return jsonEncode({'code': 0, 'data': distance, 'msg': '距离计算成功'});
    } catch (e) {
      debugPrint('$_logTag 计算距离失败: $e');
      return jsonEncode({'code': -1, 'data': 0.0, 'msg': '计算距离失败: $e'});
    }
  }

  /// 开始位置监听
  /// 参数:
  /// - accuracy: 定位精度
  /// - distanceFilter: 距离过滤器（米）
  /// - timeInterval: 时间间隔（毫秒，仅Android）
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> startPositionStreamApi({
    String? accuracy,
    int? distanceFilter,
    int? timeInterval,
  }) async {
    try {
      // 检查定位服务
      if (!await isLocationServiceEnabled()) {
        return jsonEncode({'code': 10010, 'data': false, 'msg': '定位服务未开启'});
      }

      // 检查权限
      LocationPermission permission = await checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await requestPermission();
        if (permission == LocationPermission.denied) {
          return jsonEncode({'code': 10011, 'data': false, 'msg': '定位权限被拒绝'});
        }
      }
      if (permission == LocationPermission.deniedForever) {
        return jsonEncode({'code': 10012, 'data': false, 'msg': '定位权限被永久拒绝'});
      }

      // 如果已经在监听，先停止
      if (_positionStreamSubscription != null) {
        await _positionStreamSubscription!.cancel();
        _positionStreamSubscription = null;
      }

      LocationAccuracy locationAccuracy = _getAccuracy(accuracy);

      Stream<Position> positionStream = getPositionStream(
        accuracy: locationAccuracy,
        distanceFilter: distanceFilter ?? 0,
        timeInterval: timeInterval ?? 5000,
      );

      _positionStreamSubscription = positionStream.listen(
        (Position position) {
          // 发送位置更新到WebView
          String positionJson =
              jsonEncode({'code': 0, 'data': position.toJson(), 'msg': '位置更新'});
          _positionStreamController.add(positionJson);
        },
        onError: (error) {
          debugPrint('$_logTag 位置监听错误: $error');
          String errorJson =
              jsonEncode({'code': -1, 'data': null, 'msg': '位置监听错误: $error'});
          _positionStreamController.add(errorJson);
        },
      );

      return jsonEncode({'code': 0, 'data': true, 'msg': '开始位置监听成功'});
    } catch (e) {
      debugPrint('$_logTag 开始位置监听失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '开始位置监听失败: $e'});
    }
  }

  /// 停止位置监听
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> stopPositionStreamApi() async {
    try {
      if (_positionStreamSubscription != null) {
        await _positionStreamSubscription!.cancel();
        _positionStreamSubscription = null;
        return jsonEncode({'code': 0, 'data': true, 'msg': '停止位置监听成功'});
      } else {
        return jsonEncode({'code': 0, 'data': false, 'msg': '没有正在进行的位置监听'});
      }
    } catch (e) {
      debugPrint('$_logTag 停止位置监听失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '停止位置监听失败: $e'});
    }
  }

  /// 打开应用设置
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> openAppSettingsApi() async {
    try {
      bool result = await openAppSettings();
      return jsonEncode(
          {'code': 0, 'data': result, 'msg': result ? '打开应用设置成功' : '打开应用设置失败'});
    } catch (e) {
      debugPrint('$_logTag 打开应用设置失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '打开应用设置失败: $e'});
    }
  }

  /// 打开定位设置
  /// 返回JSON格式: {"code": 0, "data": true/false, "msg": ""}
  Future<String> openLocationSettingsApi() async {
    try {
      bool result = await openLocationSettings();
      return jsonEncode(
          {'code': 0, 'data': result, 'msg': result ? '打开定位设置成功' : '打开定位设置失败'});
    } catch (e) {
      debugPrint('$_logTag 打开定位设置失败: $e');
      return jsonEncode({'code': -1, 'data': false, 'msg': '打开定位设置失败: $e'});
    }
  }

  /// 释放资源
  void dispose() {
    _positionStreamSubscription?.cancel();
    _positionStreamController.close();
  }
}
