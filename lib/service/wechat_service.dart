import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluwx/fluwx.dart' as fluwx;
import 'package:fluwx/fluwx.dart';
import 'package:npemployee/Utils/toast_utils.dart';

class WeChatService {
  static final WeChatService _singleton = WeChatService._internal();
  late final fluwx.Fluwx fluwxInstance;
  static final _logTag = "WeChatService"; // 特殊的日志标识符

  factory WeChatService() {
    return _singleton;
  }

  WeChatService._internal() {
    fluwxInstance = fluwx.Fluwx();
    _initializeWeChat(); // 初始化微信
  }

  // 检查微信是否安装
  Future<bool> checkIfWeChatInstalled() async {
    return await fluwxInstance.isWeChatInstalled;
  }

  // 打开微信小程序
  Future<void> launchWeChatMiniProgram(
      {required String userName, String? path, int type = 0}) async {
    try {
      bool isInstalled = await checkIfWeChatInstalled();
      if (!isInstalled) {
        print("[$_logTag] 微信未安装，无法打开小程序");
        ToastUtils.show('微信未安装');
        return;
      }

      print("[$_logTag] 正在打开微信小程序...");
      // 调用 open 方法，传入 MiniProgram 类型的参数
      await fluwxInstance.open(
        target: MiniProgram(
          username: userName, // 小程序的原始ID
          path: path, // 可选的小程序路径
          miniProgramType:
              WXMiniProgramType.values[type], // 小程序的版本（正式版、开发版或体验版）
        ),
      );
    } catch (e) {
      print("[$_logTag] 打开小程序失败: $e");
    }
  }

  // 打开企业微信客服
  Future<void> launchWeChatWork() async {
    try {
      bool isInstalled = await checkIfWeChatInstalled();
      if (!isInstalled) {
        print("[$_logTag] 微信未安装，无法打开企业微信");
        ToastUtils.show('微信未安装');
        return;
      }

      print("[$_logTag] 正在打开企业微信...");
      // 调用 open 方法，传入 MiniProgram 类型的参数
      await fluwxInstance.open(
        target: CustomerServiceChat(
            corpId: 'wwd2b539e9aa054302',
            url: 'https://work.weixin.qq.com/kfid/kfcd620f4febaa42f6f'),
      );
    } catch (e) {
      print("[$_logTag] 跳转企业微信客服失败: $e");
    }
  }

  // 初始化微信
  void _initializeWeChat() {
    print("[$_logTag] 初始化微信...");
    registerWeChat(); // 注册微信
  }

  Future<void> registerWeChat() async {
    try {
      print("[$_logTag] 注册微信 API...");
      await fluwxInstance.registerApi(
        appId: "wxcfff833da12005ba",
        doOnAndroid: true,
        doOnIOS: true,
        universalLink: "https://xintujing.online/xtjr/",
      );
      print("[$_logTag] 微信 API 注册成功");
    } catch (e) {
      print("[$_logTag] 微信 API 注册失败: $e");
    }
  }
}
