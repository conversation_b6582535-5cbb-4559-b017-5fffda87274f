import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:daily_pedometer/daily_pedometer.dart';
import 'package:flutter/foundation.dart';
import 'package:health/health.dart';
import 'package:npemployee/methodchannel/health_kit_channel.dart';
import 'package:permission_handler/permission_handler.dart';

/// 一个封装了步数获取功能的单例服务类。
/// 支持Android和iOS平台的步数获取
/// 
/// 主要功能：
/// - 检查步数权限
/// - 请求步数权限
/// - 获取今日步数
/// - 获取历史步数
/// - 监听步数变化
class StepCountService {
  static StepCountService? _instance;
  
  // Health插件实例（iOS）
  final Health _health = Health();
  final List<HealthDataType> _types = [HealthDataType.STEPS];
  final List<HealthDataAccess> _permissions = [HealthDataAccess.READ_WRITE];
  
  // DailyPedometer实例（Android）
  DailyPedometer? _pedometer;
  
  // 步数流订阅
  StreamSubscription? _stepCountSubscription;
  
  // 私有构造函数
  StepCountService._internal() {
    debugPrint('StepCountService 初始化');
  }
  
  // 单例工厂构造函数
  factory StepCountService() {
    _instance ??= StepCountService._internal();
    return _instance!;
  }
  
  /// 检查步数权限是否已授权
  /// 返回JSON格式的结果
  Future<String> checkStepPermissionApi() async {
    try {
      bool hasPermission = false;
      
      if (Platform.isAndroid) {
        // Android平台检查活动识别权限
        PermissionStatus status = await Permission.activityRecognition.status;
        hasPermission = status.isGranted;
      } else {
        // iOS平台检查HealthKit权限
        hasPermission = await HealthKitChannel().isHealthKitAuthorized();
      }
      
      return jsonEncode({
        'code': 0,
        'data': hasPermission,
        'msg': hasPermission ? '已授权步数权限' : '未授权步数权限'
      });
    } catch (e) {
      debugPrint('检查步数权限失败: $e');
      return jsonEncode({
        'code': -1,
        'data': false,
        'msg': '检查步数权限失败: $e'
      });
    }
  }
  
  /// 请求步数权限
  /// 返回JSON格式的结果
  Future<String> requestStepPermissionApi() async {
    try {
      bool granted = false;
      
      if (Platform.isAndroid) {
        // Android平台请求活动识别权限
        PermissionStatus status = await Permission.activityRecognition.request();
        granted = status.isGranted;
      } else {
        // iOS平台请求HealthKit权限
        bool permission = await _health.requestAuthorization(_types, permissions: _permissions);
        if (permission) {
          granted = await HealthKitChannel().isHealthKitAuthorized();
        }
      }
      
      return jsonEncode({
        'code': 0,
        'data': granted,
        'msg': granted ? '步数权限授权成功' : '步数权限授权失败'
      });
    } catch (e) {
      debugPrint('请求步数权限失败: $e');
      return jsonEncode({
        'code': -1,
        'data': false,
        'msg': '请求步数权限失败: $e'
      });
    }
  }
  
  /// 获取今日步数
  /// 返回JSON格式的结果
  Future<String> getTodayStepCountApi() async {
    try {
      int stepCount = 0;
      
      if (Platform.isAndroid) {
        // Android平台使用DailyPedometer
        if (await Permission.activityRecognition.status == PermissionStatus.granted) {
          _pedometer ??= DailyPedometer();
          await _pedometer!.initialize(false);
          stepCount = _pedometer!.steps;
        } else {
          return jsonEncode({
            'code': -1,
            'data': 0,
            'msg': '未授权活动识别权限'
          });
        }
      } else {
        // iOS平台使用Health插件
        bool authorized = await HealthKitChannel().isHealthKitAuthorized();
        if (authorized) {
          final now = DateTime.now();
          final midnight = DateTime(now.year, now.month, now.day);
          stepCount = await _health.getTotalStepsInInterval(midnight, now) ?? 0;
        } else {
          return jsonEncode({
            'code': -1,
            'data': 0,
            'msg': '未授权HealthKit权限'
          });
        }
      }
      
      return jsonEncode({
        'code': 0,
        'data': stepCount,
        'msg': '获取今日步数成功'
      });
    } catch (e) {
      debugPrint('获取今日步数失败: $e');
      return jsonEncode({
        'code': -1,
        'data': 0,
        'msg': '获取今日步数失败: $e'
      });
    }
  }
  
  /// 获取指定日期的步数
  /// [date] 指定日期
  /// 返回JSON格式的结果
  Future<String> getStepCountByDateApi(DateTime date) async {
    try {
      int stepCount = 0;
      
      if (Platform.isAndroid) {
        // Android平台目前只支持今日步数
        if (date.day == DateTime.now().day && 
            date.month == DateTime.now().month && 
            date.year == DateTime.now().year) {
          return await getTodayStepCountApi();
        } else {
          return jsonEncode({
            'code': -1,
            'data': 0,
            'msg': 'Android平台暂不支持获取历史步数'
          });
        }
      } else {
        // iOS平台使用Health插件获取指定日期步数
        bool authorized = await HealthKitChannel().isHealthKitAuthorized();
        if (authorized) {
          final startOfDay = DateTime(date.year, date.month, date.day);
          final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);
          stepCount = await _health.getTotalStepsInInterval(startOfDay, endOfDay) ?? 0;
        } else {
          return jsonEncode({
            'code': -1,
            'data': 0,
            'msg': '未授权HealthKit权限'
          });
        }
      }
      
      return jsonEncode({
        'code': 0,
        'data': stepCount,
        'msg': '获取指定日期步数成功'
      });
    } catch (e) {
      debugPrint('获取指定日期步数失败: $e');
      return jsonEncode({
        'code': -1,
        'data': 0,
        'msg': '获取指定日期步数失败: $e'
      });
    }
  }
  
  /// 开始监听步数变化
  /// 返回JSON格式的结果
  Future<String> startStepCountStreamApi() async {
    try {
      if (Platform.isAndroid) {
        // Android平台使用DailyPedometer监听
        if (await Permission.activityRecognition.status == PermissionStatus.granted) {
          _pedometer ??= DailyPedometer();
          await _pedometer!.initialize(false);
          
          _stepCountSubscription = _pedometer!.dailyStepCountStream.listen((stepCount) {
            debugPrint('步数变化: $stepCount');
            // 这里可以添加回调通知
          });
          
          return jsonEncode({
            'code': 0,
            'data': true,
            'msg': '开始监听步数变化成功'
          });
        } else {
          return jsonEncode({
            'code': -1,
            'data': false,
            'msg': '未授权活动识别权限'
          });
        }
      } else {
        // iOS平台暂不支持实时监听
        return jsonEncode({
          'code': -1,
          'data': false,
          'msg': 'iOS平台暂不支持实时步数监听'
        });
      }
    } catch (e) {
      debugPrint('开始监听步数变化失败: $e');
      return jsonEncode({
        'code': -1,
        'data': false,
        'msg': '开始监听步数变化失败: $e'
      });
    }
  }
  
  /// 停止监听步数变化
  /// 返回JSON格式的结果
  Future<String> stopStepCountStreamApi() async {
    try {
      if (_stepCountSubscription != null) {
        await _stepCountSubscription!.cancel();
        _stepCountSubscription = null;
        
        return jsonEncode({
          'code': 0,
          'data': true,
          'msg': '停止监听步数变化成功'
        });
      } else {
        return jsonEncode({
          'code': 0,
          'data': false,
          'msg': '没有正在进行的步数监听'
        });
      }
    } catch (e) {
      debugPrint('停止监听步数变化失败: $e');
      return jsonEncode({
        'code': -1,
        'data': false,
        'msg': '停止监听步数变化失败: $e'
      });
    }
  }
  
  /// 打开应用设置页面
  /// 返回JSON格式的结果
  Future<String> openAppSettingsApi() async {
    try {
      bool opened = await openAppSettings();
      return jsonEncode({
        'code': 0,
        'data': opened,
        'msg': opened ? '打开应用设置成功' : '打开应用设置失败'
      });
    } catch (e) {
      debugPrint('打开应用设置失败: $e');
      return jsonEncode({
        'code': -1,
        'data': false,
        'msg': '打开应用设置失败: $e'
      });
    }
  }
  
  /// 释放资源
  void dispose() {
    _stepCountSubscription?.cancel();
    _stepCountSubscription = null;
    debugPrint('StepCountService 资源已释放');
  }
}
