/// <AUTHOR>
/// @project FlutterKit
/// @date 8/6/23

// import 'package:flutter/foundation.dart';
// import 'package:flutter/gestures.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:geolocator/geolocator.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/custom_dialog.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/bluetooth_service.dart';
import 'package:npemployee/service/location_service.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:webview_flutter/webview_flutter.dart';

import 'package:webview_flutter_android/webview_flutter_android.dart';

class WebviewScreenPage extends StatefulWidget {
  final String? title;
  final String url;
  final bool? needLandscape;
  final bool? needNav;
  final bool? needBackBtn; //横屏时，控制返回按钮
  final LinearGradient? gradient; //首页同行朋友圈使用
  final bool enableLongPress; //是否启用长按事件
  final bool keepAlive;
  const WebviewScreenPage({
    super.key,
    required this.url,
    this.title,
    this.needLandscape = false,
    this.needNav = true,
    this.gradient,
    this.needBackBtn = false,
    required this.enableLongPress,
    required this.keepAlive,
  });

  @override
  State<WebviewScreenPage> createState() => _WebviewScreenPageState();
}

class _WebviewScreenPageState extends State<WebviewScreenPage>
    with AutomaticKeepAliveClientMixin {
  late final WebViewController controller;
  double progress = 0;
  StreamSubscription? _scanResultsSubscription;
  StreamSubscription<String>? _positionStreamSubscription;

  String get name => GlobalPreferences().userInfo?.user.name ?? '';
  String get mobile => GlobalPreferences().userInfo?.user.mobile ?? '';
  String get id_no => GlobalPreferences().userInfo?.user.id_no ?? '';
  String get departmentType =>
      GlobalPreferences().userInfo?.departmentTypeMin ?? '';
  String get departmentName =>
      GlobalPreferences().userInfo?.departmentNameMin ?? '';
  String get avatar => GlobalPreferences().userInfo?.user.avatar ?? '';
  String get token => GlobalPreferences().tokenValue ?? '';
  bool get needProgress =>
      widget.url != AppInfo().growthChartUrl &&
      widget.url != AppInfo().friendCircleUrl; //增长榜,同行朋友圈不显示进度条

  bool loadSuccess = false;
  bool loadHasError = false;

  @override
  bool get wantKeepAlive => widget.keepAlive; // true-保持状态 false-不保持

  @override
  void initState() {
    super.initState();
    if (widget.needLandscape ?? false) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    }

    _initializeWebView();

    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
  }

  @override
  void didUpdateWidget(covariant WebviewScreenPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.url != widget.url || !loadSuccess) {
      controller.loadRequest(Uri.parse(widget.url));
    }
  }

  @override
  void dispose() {
    // 取消蓝牙扫描结果监听
    _scanResultsSubscription?.cancel();
    // 取消位置监听
    _positionStreamSubscription?.cancel();

    if (widget.needLandscape ?? false) {
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitDown,
        DeviceOrientation.portraitUp,
      ]);
    }
    super.dispose();
  }

  void _initializeWebView() {
    controller = WebViewController()
      ..setUserAgent('XtjrFlutterAppWebview')
      ..setBackgroundColor(
          widget.gradient != null ? Colors.transparent : Colors.white)
      ..setJavaScriptMode(JavaScriptMode.unrestricted) //允许javascript
      ..addJavaScriptChannel('xtjrChannel',
          onMessageReceived: _jsChannelHandler)
      ..setOnConsoleMessage((mess) {
        debugPrint('---- web打印 ${mess.message}');
      })
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageStarted: (url) {
            debugPrint('--------  web onPageStarted $url');
            loadSuccess = false;
            loadHasError = false;
          },
          onPageFinished: (url) {
            debugPrint('--------  web onPageFinished $url');
            if (loadHasError) {
              loadSuccess = false;
            } else {
              loadSuccess = true;
            }
            controller.runJavaScript('''window.raffleForm = {
  name: '$name',
  mobile: '$mobile',
  id_no: '$id_no',
  department_type: '$departmentType',
  department_name: '$departmentName',
  avatar: '$avatar',
  token: '$token',
};
window.raffleFormInjected()
console.log('onPageFinished window.raffleForm', window.raffleForm);
''');
            //自适应手机屏幕
            controller.runJavaScript('''
            document.querySelector('meta[name="viewport"]')?.setAttribute(
              'content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
            );
          ''');
          },
          onProgress: (p) {
            setState(() {
              progress = p / 100;
            });
          },
          onWebResourceError: (error) {
            debugPrint('--------  web onWebResourceError $error');
            loadHasError = true;
          },
          onUrlChange: (change) {},
          onHttpAuthRequest: (request) {},
          onHttpError: (error) {
            debugPrint('--------  web onHttpError $error');
            loadHasError = true;
          },
          onNavigationRequest: (request) {
            debugPrint('--------  web onNavigationRequest ${request.url}');
            if (request.url.startsWith('https://nn.xtjzx.cn/scheme.html')) {
              _launchWeChatMiniProgram(request.url);
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return widget.needNav ?? true
        ? Scaffold(
            appBar: CommonNav(
              title: widget.title ?? "",
              onBack: () {
                controller.canGoBack().then((v) {
                  if (v) {
                    controller.goBack();
                  } else {
                    NavigatorUtils.pop(context);
                  }
                });
              },
            ),
            body: _webViewBuilder(),
          )
        : Material(
            color: Colors.white,
            child: Stack(
              children: [
                Container(
                  height: 130.h,
                  decoration: BoxDecoration(gradient: widget.gradient),
                ),
                _webViewBuilder(),
                if (widget.needBackBtn ?? false)
                  Positioned(
                      top: 16.h,
                      // left: 16.w,
                      child: IconButton(
                        onPressed: () async {
                          if (widget.needLandscape ?? false) {
                            await SystemChrome.setPreferredOrientations([
                              DeviceOrientation.portraitDown,
                              DeviceOrientation.portraitUp,
                            ]);
                          }
                          NavigatorUtils.pop(context);
                        },
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: Color(0xFF000000),
                        ),
                        iconSize: 18,
                      )),
              ],
            ),
          );
  }

  /// ---------------------------- jschannel
  void _jsChannelHandler(JavaScriptMessage message) {
    final res = jsonDecode(message.message);
    String action = res['action'];
    Map data = res['data'] ?? {};
    if (action == 'getLocation') {
      debugPrint('-------- 收到 web geoLocation 消息');
      _getPosition(data);
    } else if (action == 'exit') {
      debugPrint('-------- 收到 web exit 消息');
      NavigatorUtils.pop(context);
    } else if (action == 'openLocationSetting') {
      if (Platform.isAndroid) {
        LocationService().openLocationSettings();
      } else if (Platform.isIOS) {
        LocationService().openAppSettings();
      }
    }
    // 蓝牙相关功能
    else if (action == 'isBluetoothSupported') {
      debugPrint('-------- 收到 web isBluetoothSupported 消息');
      _isBluetoothSupported();
    } else if (action == 'getBluetoothState') {
      debugPrint('-------- 收到 web getBluetoothState 消息');
      _getBluetoothState();
    } else if (action == 'turnOnBluetooth') {
      debugPrint('-------- 收到 web turnOnBluetooth 消息');
      _turnOnBluetooth();
    } else if (action == 'startScan') {
      debugPrint('-------- 收到 web startScan 消息');
      _startScan(data);
    } else if (action == 'stopScan') {
      debugPrint('-------- 收到 web stopScan 消息');
      _stopScan();
    } else if (action == 'getScanResults') {
      debugPrint('-------- 收到 web getScanResults 消息');
      _getScanResults();
    } else if (action == 'connectToDevice') {
      debugPrint('-------- 收到 web connectToDevice 消息');
      _connectToDevice(data);
    } else if (action == 'disconnectDevice') {
      debugPrint('-------- 收到 web disconnectDevice 消息');
      _disconnectDevice();
    } else if (action == 'getConnectedDevice') {
      debugPrint('-------- 收到 web getConnectedDevice 消息');
      _getConnectedDevice();
    } else if (action == 'discoverServices') {
      debugPrint('-------- 收到 web discoverServices 消息');
      _discoverServices();
    } else if (action == 'readCharacteristic') {
      debugPrint('-------- 收到 web readCharacteristic 消息');
      _readCharacteristic(data);
    } else if (action == 'writeCharacteristic') {
      debugPrint('-------- 收到 web writeCharacteristic 消息');
      _writeCharacteristic(data);
    } else if (action == 'setNotification') {
      debugPrint('-------- 收到 web setNotification 消息');
      _setNotification(data);
    } else if (action == 'getSystemDevices') {
      debugPrint('-------- 收到 web getSystemDevices 消息');
      _getSystemDevices();
    } else if (action == 'scanResultsListen') {
      // 监听蓝牙扫描结果
      _scanResultsSubscription =
          BluetoothService().scanResultsStream.listen((result) {
        if (mounted) {
          controller.runJavaScript(
              'window.bluetoothCallback("scanResultsUpdate", $result)');
        }
      });
    }
    // 定位相关功能
    else if (action == 'isLocationServiceEnabled') {
      debugPrint('-------- 收到 web isLocationServiceEnabled 消息');
      _isLocationServiceEnabled();
    } else if (action == 'checkPermission') {
      debugPrint('-------- 收到 web checkPermission 消息');
      _checkLocationPermission();
    } else if (action == 'requestPermission') {
      debugPrint('-------- 收到 web requestPermission 消息');
      _requestLocationPermission();
    } else if (action == 'getCurrentPosition') {
      debugPrint('-------- 收到 web getCurrentPosition 消息');
      _getCurrentLocationPosition(data);
    } else if (action == 'getLastKnownPosition') {
      debugPrint('-------- 收到 web getLastKnownPosition 消息');
      _getLastKnownLocationPosition();
    } else if (action == 'getLocationAccuracy') {
      debugPrint('-------- 收到 web getLocationAccuracy 消息');
      _getLocationAccuracy();
    } else if (action == 'calculateDistance') {
      debugPrint('-------- 收到 web calculateDistance 消息');
      _calculateLocationDistance(data);
    } else if (action == 'startPositionStream') {
      debugPrint('-------- 收到 web startPositionStream 消息');
      _startLocationPositionStream(data);
    } else if (action == 'stopPositionStream') {
      debugPrint('-------- 收到 web stopPositionStream 消息');
      _stopLocationPositionStream();
    } else if (action == 'openAppSettings') {
      debugPrint('-------- 收到 web openAppSettings 消息');
      _openLocationAppSettings();
    } else if (action == 'openLocationSettings') {
      debugPrint('-------- 收到 web openLocationSettings 消息');
      _openLocationLocationSettings();
    } else if (action == 'positionStreamListen') {
      // 监听位置更新
      _positionStreamSubscription =
          LocationService().positionStream.listen((result) {
        if (mounted) {
          controller.runJavaScript(
              'window.locationCallback("positionUpdate", $result)');
        }
      });
    }
  }

  /// ---------------------------- 定位
  void _getPosition(Map data) {
    LocationService()
        .getPosition(
            timeOut: data['timeOut'],
            locationAccuracy: data['locationAccuracy'],
            distanceFilter: data['distanceFilter'])
        .then((value) {
      controller.runJavaScript('getPositionHandle($value)');
    });
  }

  /// ---------------------------- 微信小程序
  void _launchWeChatMiniProgram(String url) {
    showDialog(
        context: context,
        builder: (_) {
          return CustomDialog(
            title: '提示',
            content: '进入微信小程序打开此页面',
            confirmButtonText: '打开',
            onCancel: () {
              NavigatorUtils.pop(_);
            },
            onConfirm: () {
              NavigatorUtils.pop(_);
              String id = url.split('id=').last;
              WeChatService().launchWeChatMiniProgram(
                  userName: 'gh_823762b08f2e',
                  path: 'pages/sharePage/sharePage?id=$id');
            },
          );
        });
  }

  /// ---------------------------- 蓝牙相关方法

  /// 检查蓝牙是否支持
  void _isBluetoothSupported() async {
    String result = await BluetoothService().isBluetoothSupported();
    controller.runJavaScript(
        'window.bluetoothCallback("isBluetoothSupported", $result)');
  }

  /// 获取蓝牙状态
  void _getBluetoothState() async {
    String result = await BluetoothService().getBluetoothState();
    controller.runJavaScript(
        'window.bluetoothCallback("getBluetoothState", $result)');
  }

  /// 打开蓝牙
  void _turnOnBluetooth() async {
    String result = await BluetoothService().turnOnBluetooth();
    controller
        .runJavaScript('window.bluetoothCallback("turnOnBluetooth", $result)');
  }

  /// 开始扫描蓝牙设备
  void _startScan(Map data) async {
    int? timeout = data['timeout'] != null
        ? int.tryParse(data['timeout'].toString())
        : null;
    List<String>? withServices = data['withServices'] != null
        ? List<String>.from(data['withServices'])
        : null;
    List<String>? withNames =
        data['withNames'] != null ? List<String>.from(data['withNames']) : null;

    String result = await BluetoothService().startScan(
      timeout: timeout,
      withServices: withServices,
      withNames: withNames,
    );
    controller.runJavaScript('window.bluetoothCallback("startScan", $result)');
  }

  /// 停止扫描蓝牙设备
  void _stopScan() async {
    String result = await BluetoothService().stopScan();
    controller.runJavaScript('window.bluetoothCallback("stopScan", $result)');
  }

  /// 获取扫描结果
  void _getScanResults() async {
    String result = await BluetoothService().getScanResults();
    controller
        .runJavaScript('window.bluetoothCallback("getScanResults", $result)');
  }

  /// 连接到蓝牙设备
  void _connectToDevice(Map data) async {
    String deviceId = data['deviceId'] ?? '';
    bool autoConnect = data['autoConnect'] == true;
    int? timeout = data['timeout'] != null
        ? int.tryParse(data['timeout'].toString())
        : null;

    String result = await BluetoothService().connectToDevice(
      deviceId,
      autoConnect: autoConnect,
      timeout: timeout,
    );
    controller
        .runJavaScript('window.bluetoothCallback("connectToDevice", $result)');
  }

  /// 断开蓝牙设备连接
  void _disconnectDevice() async {
    String result = await BluetoothService().disconnectDevice();
    controller
        .runJavaScript('window.bluetoothCallback("disconnectDevice", $result)');
  }

  /// 获取当前连接的设备信息
  void _getConnectedDevice() async {
    String result = await BluetoothService().getConnectedDevice();
    controller.runJavaScript(
        'window.bluetoothCallback("getConnectedDevice", $result)');
  }

  /// 发现设备服务
  void _discoverServices() async {
    String result = await BluetoothService().discoverServices();
    controller
        .runJavaScript('window.bluetoothCallback("discoverServices", $result)');
  }

  /// 读取特征值
  void _readCharacteristic(Map data) async {
    String serviceUuid = data['serviceUuid'] ?? '';
    String characteristicUuid = data['characteristicUuid'] ?? '';

    String result = await BluetoothService().readCharacteristic(
      serviceUuid,
      characteristicUuid,
    );
    controller.runJavaScript(
        'window.bluetoothCallback("readCharacteristic", $result)');
  }

  /// 写入特征值
  void _writeCharacteristic(Map data) async {
    String serviceUuid = data['serviceUuid'] ?? '';
    String characteristicUuid = data['characteristicUuid'] ?? '';
    String value = data['value'] ?? '';
    bool withoutResponse = data['withoutResponse'] == true;

    String result = await BluetoothService().writeCharacteristic(
      serviceUuid,
      characteristicUuid,
      value,
      withoutResponse: withoutResponse,
    );
    controller.runJavaScript(
        'window.bluetoothCallback("writeCharacteristic", $result)');
  }

  /// 设置特征通知
  void _setNotification(Map data) async {
    String serviceUuid = data['serviceUuid'] ?? '';
    String characteristicUuid = data['characteristicUuid'] ?? '';
    bool enable = data['enable'] == true;

    String result = await BluetoothService().setNotification(
      serviceUuid,
      characteristicUuid,
      enable,
    );
    controller
        .runJavaScript('window.bluetoothCallback("setNotification", $result)');
  }

  /// 获取系统设备
  void _getSystemDevices() async {
    String result = await BluetoothService().getSystemDevices();
    controller
        .runJavaScript('window.bluetoothCallback("getSystemDevices", $result)');
  }

  Future<void> _saveImage(String imageUrl) async {
    if (await _requestPermission()) {
      try {
        final imageData =
            await NetworkAssetBundle(Uri.parse(imageUrl)).load("");
        final bytes = imageData.buffer.asUint8List();
        final result = await ImageGallerySaver.saveImage(bytes);
        if (result['isSuccess']) {
          ToastUtils.show('图片保存成功');
        } else {
          ToastUtils.show('图片保存失败');
        }
      } catch (e) {
        print(e);
        ToastUtils.show('图片保存失败: $e');
      }
    }
  }

  Future<bool> _requestPermission() async {
    PermissionStatus status = await Permission.storage.request();
    return status.isGranted;
  }

  // ==================== 定位相关方法 ====================

  /// 检查定位服务是否启用
  void _isLocationServiceEnabled() async {
    String result = await LocationService().isLocationServiceEnabledApi();
    controller.runJavaScript(
        'window.locationCallback("isLocationServiceEnabled", $result)');
  }

  /// 检查定位权限
  void _checkLocationPermission() async {
    String result = await LocationService().checkPermissionApi();
    controller
        .runJavaScript('window.locationCallback("checkPermission", $result)');
  }

  /// 请求定位权限
  void _requestLocationPermission() async {
    String result = await LocationService().requestPermissionApi();
    controller
        .runJavaScript('window.locationCallback("requestPermission", $result)');
  }

  /// 获取当前位置
  void _getCurrentLocationPosition(Map data) async {
    String? accuracy = data['accuracy'];
    int? timeLimit = data['timeLimit'] != null
        ? int.tryParse(data['timeLimit'].toString())
        : null;

    String result = await LocationService().getCurrentPositionApi(
      accuracy: accuracy,
      timeLimit: timeLimit,
    );
    controller.runJavaScript(
        'window.locationCallback("getCurrentPosition", $result)');
  }

  /// 获取最后已知位置
  void _getLastKnownLocationPosition() async {
    String result = await LocationService().getLastKnownPositionApi();
    controller.runJavaScript(
        'window.locationCallback("getLastKnownPosition", $result)');
  }

  /// 获取定位精度
  void _getLocationAccuracy() async {
    String result = await LocationService().getLocationAccuracyApi();
    controller.runJavaScript(
        'window.locationCallback("getLocationAccuracy", $result)');
  }

  /// 计算距离
  void _calculateLocationDistance(Map data) async {
    double? startLatitude = data['startLatitude'] != null
        ? double.tryParse(data['startLatitude'].toString())
        : null;
    double? startLongitude = data['startLongitude'] != null
        ? double.tryParse(data['startLongitude'].toString())
        : null;
    double? endLatitude = data['endLatitude'] != null
        ? double.tryParse(data['endLatitude'].toString())
        : null;
    double? endLongitude = data['endLongitude'] != null
        ? double.tryParse(data['endLongitude'].toString())
        : null;

    if (startLatitude == null ||
        startLongitude == null ||
        endLatitude == null ||
        endLongitude == null) {
      String errorResult =
          jsonEncode({'code': -1, 'data': 0.0, 'msg': '坐标参数无效'});
      controller.runJavaScript(
          'window.locationCallback("calculateDistance", $errorResult)');
      return;
    }

    String result = await LocationService().calculateDistanceApi(
      startLatitude: startLatitude,
      startLongitude: startLongitude,
      endLatitude: endLatitude,
      endLongitude: endLongitude,
    );
    controller
        .runJavaScript('window.locationCallback("calculateDistance", $result)');
  }

  /// 开始位置监听
  void _startLocationPositionStream(Map data) async {
    String? accuracy = data['accuracy'];
    int? distanceFilter = data['distanceFilter'] != null
        ? int.tryParse(data['distanceFilter'].toString())
        : null;
    int? timeInterval = data['timeInterval'] != null
        ? int.tryParse(data['timeInterval'].toString())
        : null;

    String result = await LocationService().startPositionStreamApi(
      accuracy: accuracy,
      distanceFilter: distanceFilter,
      timeInterval: timeInterval,
    );
    controller.runJavaScript(
        'window.locationCallback("startPositionStream", $result)');
  }

  /// 停止位置监听
  void _stopLocationPositionStream() async {
    String result = await LocationService().stopPositionStreamApi();
    controller.runJavaScript(
        'window.locationCallback("stopPositionStream", $result)');
  }

  /// 打开应用设置
  void _openLocationAppSettings() async {
    String result = await LocationService().openAppSettingsApi();
    controller
        .runJavaScript('window.locationCallback("openAppSettings", $result)');
  }

  /// 打开定位设置
  void _openLocationLocationSettings() async {
    String result = await LocationService().openLocationSettingsApi();
    controller.runJavaScript(
        'window.locationCallback("openLocationSettings", $result)');
  }

  Widget _webViewBuilder() {
    return Column(
      children: [
        progress < 1.0 && needProgress
            ? LinearProgressIndicator(
                value: progress,
                color: AppTheme.colorBlue,
              )
            : Container(),
        Expanded(
            child: GestureDetector(
          onLongPress: !widget.enableLongPress
              ? null
              : () async {
                  final result = await controller.runJavaScriptReturningResult(
                      "document.getElementsByTagName('img')[0].src");
                  final String? imageUrl =
                      jsonDecode(result as String) as String?;
                  if (imageUrl != null && imageUrl.isNotEmpty) {
                    _saveImage(imageUrl);
                  }
                },
          // onHorizontalDragEnd: (details) async {
          //   if (details.primaryVelocity != null) {
          //     if (details.primaryVelocity! > 0) {
          //       if (await controller.canGoBack()) {
          //         controller.goBack();
          //       }
          //     } else if (details.primaryVelocity! < 0) {
          //       if (await controller.canGoForward()) {
          //         controller.goForward();
          //       }
          //     }
          //   }
          // },
          child: WebViewWidget(
            controller: controller,
          ),
        ))
      ],
    );
  }
}



/* class WebViewScreen extends StatefulWidget {
  final String title;
  final String selectedUrl;
  final bool? noTitle;

  WebViewScreen(
      {required this.title, required this.selectedUrl, this.noTitle = false});

  @override
  _WebViewScreenState createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();
    _controller = WebViewController.fromPlatformCreationParams(
      const PlatformWebViewControllerCreationParams(),
    );
    _controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    _controller
        .setUserAgent('Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)');
    _controller.loadRequest(Uri.parse(widget.selectedUrl));
    _controller.enableZoom(true);
    _controller.setBackgroundColor(Colors.transparent);

    _controller.setNavigationDelegate(
      NavigationDelegate(
        onPageStarted: (url) {
          EasyLoading.show();
        },
        onPageFinished: (url) {
          // 注入 viewport 配置，确保自适应屏幕
          EasyLoading.dismiss();
          _controller.runJavaScript('''
            document.querySelector('meta[name="viewport"]')?.setAttribute(
              'content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
            );
          ''');
        },
        onHttpError: (error) {
          EasyLoading.dismiss();
        },
        onWebResourceError: (WebResourceError error) {
          EasyLoading.dismiss();
          debugPrint('''
      Page resource error:
      code: ${error.errorCode}
      description: ${error.description}
      errorType: ${error.errorType}
      isForMainFrame: ${error.isForMainFrame}
      ''');
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (await _controller.canGoBack()) {
          _controller.goBack();
          return Future.value(false); // prevent the widget from being popped
        } else {
          return Future.value(true); // allow the widget to be popped
        }
      },
      child: widget.noTitle != null && widget.noTitle == false
          ? Scaffold(
              appBar: AppBar(
                centerTitle: true,
                title: Text(widget.title),
              ),
              body: WebViewWidget(
                controller: _controller,
                gestureRecognizers: {
                  Factory<VerticalDragGestureRecognizer>(
                      () => VerticalDragGestureRecognizer())
                },
              ),
            )
          : WebViewWidget(
              controller: _controller,
              gestureRecognizers: {
                Factory<VerticalDragGestureRecognizer>(
                    () => VerticalDragGestureRecognizer())
              },
            ),
    );
  }
}
 */