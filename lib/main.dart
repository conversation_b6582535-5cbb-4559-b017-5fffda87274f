import 'dart:async';
import 'dart:io';

import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/navigator_push_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/page/webview_screen_page.dart';
import 'package:npemployee/common/widget/easy_refresh_config.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/local_cache_manager.dart';
import 'package:npemployee/manager/pedometer_manager.dart';
import 'package:npemployee/page/home/<USER>';
import 'package:npemployee/page/login/login_bloc.dart';
import 'package:npemployee/page/login/login_page.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/route_observer.dart';
import 'package:npemployee/routers/routers.dart';
import 'package:package_info/package_info.dart';
import 'package:quick_actions/quick_actions.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:umeng_apm_sdk/umeng_apm_sdk.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

// 主应用的导航器 Key，用于全局导航
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // final UmengApmSdk umengApmSdk = UmengApmSdk(
  //   name: '',
  //   bver: '',
  //   enableLog: AppInfo.isProduction,
  //   flutterVersion: ' 3.24.3',
  //   engineVersion: '36335019a8',
  //   enableTrackingPageFps: true,
  //   enableTrackingPagePerf: true,
  //   initFlutterBinding: MyApmWidgetsFlutterBinding.ensureInitialized,
  //   // 抛出异常事件
  //   errorFilter: {'mode': 'ignore', 'rules': []},
  //   onError: (exception, stack) {
  //     print(exception);
  //     print(stack);
  //     print('-------------- aaaaaaaaaa umeng error ---------------');
  //   },
  // );
  // umengApmSdk.init(appRunner: (observer) async {

  // });

  SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
      overlays: [SystemUiOverlay.bottom, SystemUiOverlay.top]);

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarColor: Colors.transparent, // 设置状态栏颜色为透明
    statusBarIconBrightness: Brightness.dark, // 设置状态栏图标颜色为深色（黑色）
    statusBarBrightness:
        Platform.isAndroid ? Brightness.light : null, // 设置状态栏亮度为亮色（针对 Android）
  ));

  // 初始化腾讯云IM
  final CoreServicesImpl coreInstance = TIMUIKitCore.getInstance();
  bool? initState = await coreInstance.init(
      sdkAppID: AppInfo().imId,
      loglevel: LogLevelEnum.V2TIM_LOG_DEBUG,
      listener: V2TimSDKListener(
        onKickedOffline: () {
          // EasyLoading.showToast('消息离线了~');
        },
        onUserSigExpired: () {
          AppInfo().clearLoginStatu();
          navigatorKey.currentState?.pushNamedAndRemoveUntil(
              CommonRouter.loginPage, (route) => false);
        },
      ));
  if (initState == null || !initState) {
    debugPrint('----- 腾讯IM初始化失败 -----');
  }

  debugPrint('开始初始化mmkv');
  final rootDir = await LocalCacheManager.shared.init();
  debugPrint("MMKV for flutter with rootDir = $rootDir");
  debugPrint('初始化downloader');
  await FlutterDownloader.initialize(debug: AppInfo().isDebug, ignoreSsl: true);
  // if (Platform.isAndroid) {
  //   PedometerManager();
  // }

  EasyRefreshConfig.init();

  await GlobalPreferences().loadPreferences();
  MultiBlocProvider(providers: [
    BlocProvider<LoginBloc>(create: (BuildContext context) => LoginBloc()),
  ], child: const MyApp());
}

class MyApp extends StatefulWidget {
  final NavigatorObserver? navigatorObserver;

  const MyApp({
    super.key,
    this.navigatorObserver,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  String initialRoute = '/';

  late AppLinks _appLinks;
  StreamSubscription<Uri>? _linkSubscription;

  void _initDeepLinks() async {
    _appLinks = AppLinks();

    _linkSubscription = _appLinks.uriLinkStream.listen((uri) {
      debugPrint('***** open app link: ${Uri.decodeComponent(uri.path)}');
      String link = Uri.decodeComponent(uri.path);
      if (link.contains('/openApp')) {
        final regex = RegExp(r'/openApp/(.*)');
        final match = regex.firstMatch(link);
        if (match != null) {
          String extractedString = match.group(1)!;
          debugPrint('Extracted String: $extractedString');
          String valueStr = extractedString;
          NavigatorPushUtils.toWithPath(context, valueStr, () {});
        }
      }
    });
  }

  @override
  void dispose() {
    _linkSubscription?.cancel();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    Routers.initRouters();

    _initDeepLinks();

    if (!GlobalPreferences().isShouldShowProxy) {
      debugPrint('初始化友盟');
      UmengCommonSdk.initCommon(
          AppInfo().umAndroidId, AppInfo().umIosID, AppInfo().umChannel);
    }

    const QuickActions quickActions = QuickActions();
    quickActions.initialize((String shortcutType) {
      if ("扫一扫" == shortcutType) {
        if (AppInfo().hasLogined) {
          navigatorKey.currentState?.push(
            MaterialPageRoute(builder: (context) => const QrcodeScanPage()),
          );
        } else {
          navigatorKey.currentState?.push(
            MaterialPageRoute(builder: (context) => LoginPage()),
          );
          // navigatorKey.currentState?.pop();
        }
      }
    });

    quickActions.setShortcutItems(<ShortcutItem>[
      // NOTE: This first action icon will only work on iOS.
      // In a real world project keep the same file name for both platforms.
      const ShortcutItem(
        type: '扫一扫',
        localizedTitle: '扫一扫',
        localizedSubtitle: '扫一扫',
        icon: 'home_qrcode',
      ),
      // NOTE: This second action icon will only work on Android.
      // In a real world project keep the same file name for both platforms.
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return MaterialApp(
          navigatorKey: navigatorKey,
          navigatorObservers: [
            // widget.navigatorObserver ?? ApmNavigatorObserver.singleInstance,
            TUICallKit.navigatorObserver,
            KRouterObserver(),
          ],
          initialRoute: initialRoute,
          onGenerateRoute: Routers.router.generator,
          localizationsDelegates: getLocalizationsDelegates([
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate
          ]),
          supportedLocales: const [Locale('zh', 'CN')],
          localeResolutionCallback: (locale, supportedLocales) => locale,
          debugShowCheckedModeBanner: false, // Hide debug banner
          title: '新途径人',
          theme: ThemeData(
            scaffoldBackgroundColor: Colors.white,
            primaryColor: AppTheme.colorBlue,
            appBarTheme: const AppBarTheme(
              backgroundColor: Colors.white,
              elevation: 0, // Remove shadow
              titleTextStyle: TextStyle(
                fontSize: 18.0,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
            ),
          ),
          builder: EasyLoading.init(
            builder: (context, child) {
              return GestureDetector(
                onTap: () {
                  FocusScopeNode currentFocus = FocusScope.of(context);
                  if (!currentFocus.hasPrimaryFocus &&
                      currentFocus.focusedChild != null) {
                    FocusManager.instance.primaryFocus?.unfocus();
                  }
                },
                child: child,
              );
            },
          ),
        );
      },
    );
  }

  Iterable<LocalizationsDelegate> getLocalizationsDelegates(
      List<LocalizationsDelegate> newDelegates) {
    newDelegates.addAll(GlobalMaterialLocalizations.delegates);
    return newDelegates;
  }
}

class MyApmWidgetsFlutterBinding extends ApmWidgetsFlutterBinding {
  @override
  void handleAppLifecycleStateChanged(AppLifecycleState state) {
    print('AppLifecycleState changed to $state');
    super.handleAppLifecycleStateChanged(state);
  }

  static WidgetsBinding? ensureInitialized() {
    MyApmWidgetsFlutterBinding();
    return WidgetsBinding.instance;
  }
}
