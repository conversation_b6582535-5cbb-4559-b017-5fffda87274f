import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/model/study/operational_tool_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/service/wechat_service.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../common/app_theme.dart';
import '../../common/dialog/custom_dialog.dart';

class OperationalToolPage extends StatefulWidget {
  final String title;
  const OperationalToolPage({super.key, required this.title});

  @override
  State<OperationalToolPage> createState() => _OperationalToolPageState();
}

class _OperationalToolPageState extends State<OperationalToolPage> {
  // 运营工具列表数据
  List<Map<OperationalToolEdges, List<OperationalToolModel>>>
      operationalToolList = [];

  final EasyRefreshController _refreshController = EasyRefreshController();

  void _getOperationalToolList() {
    UserServiceProvider().getOperationalToolList(
        cacheCallBack: (result) {
          _formatOperationalToolList(result, true);
        },
        successCallBack: (result) {
          _formatOperationalToolList(result, false);
        },
        errorCallBack: (err) {});
  }

  void _formatOperationalToolList(ResultData? result, bool isCache) {
    operationalToolList.clear();
    Map<OperationalToolEdges, List<OperationalToolModel>> tempMap = {};

    for (var element in result?.data ?? []) {
      OperationalToolModel model =
          OperationalToolModel.fromJson(element['tools']);

      if (tempMap.containsKey(model.edges)) {
        tempMap[model.edges]!.add(model);
      } else {
        tempMap[model.edges] = [model];
      }
    }

    operationalToolList =
        tempMap.entries.map((entry) => {entry.key: entry.value}).toList();

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _getOperationalToolList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xFFF7F8FD),
        appBar: CommonNav(title: widget.title),
        body: EasyRefresh.builder(
          controller: _refreshController,
          onRefresh: () => _getOperationalToolList(),
          childBuilder: (_, physic) => operationalToolList.isEmpty
              ? NoDataPage(physics: physic)
              : ListView.builder(
                  physics: physic,
                  itemBuilder: _itemBuilder,
                  itemCount: operationalToolList.length),
        ));
  }

  void _saveImgAction(
      BuildContext dialogContext, String imageUrl, String name) async {
    var storageStatus = await Permission.storage.status;
    if (!storageStatus.isGranted) {
      showDialog(
        context: dialogContext,
        builder: (context) => CustomDialog(
          title: "提示",
          content: "保存图片到本地需要读写存储权限",
          cancelButtonText: "取消",
          confirmButtonText: "确定",
          cancelButtonColor: AppTheme.colorButtonGrey,
          confirmButtonColor: AppTheme.colorBlue,
          onCancel: () {
            Navigator.of(dialogContext).pop();
          },
          onConfirm: () async {
            Navigator.of(dialogContext).pop();
            _saveImage(context, imageUrl, name);
          },
        ),
      );
    } else {
      _saveImage(dialogContext, imageUrl, name);
    }
  }

  Future<void> _saveImage(
      BuildContext dialogContext, String imageUrl, String miniCodeName) async {
    if (await _requestPermission()) {
      try {
        final imageData =
            await NetworkAssetBundle(Uri.parse(imageUrl)).load("");
        final bytes = imageData.buffer.asUint8List();
        final result = await ImageGallerySaver.saveImage(bytes);
        if (result['isSuccess']) {
          Navigator.of(dialogContext).pop();
          ToastUtils.show("$miniCodeName小程序码图片保存成功");
        } else {
          Navigator.of(dialogContext).pop();
          ToastUtils.show("$miniCodeName小程序码图片保存失败");
        }
      } catch (e) {
        print(e);
        Navigator.of(dialogContext).pop();
        ToastUtils.show("$miniCodeName小程序码图片保存失败 $e");
      }
    }
  }

  Future<bool> _requestPermission() async {
    PermissionStatus status = await Permission.storage.request();
    return status.isGranted;
  }

  Widget _itemBuilder(BuildContext context, int index) {
    Map<OperationalToolEdges, List<OperationalToolModel>> toolMap =
        operationalToolList[index];
    OperationalToolEdges edges = toolMap.keys.first;
    List<OperationalToolModel> tools = toolMap.values.first;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(edges.name),
        ...tools.map((tool) => _buildListItem(tool)).toList(),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return (Padding(
        padding: EdgeInsets.fromLTRB(16.w, 16.h, 16.w, 8.h),
        child: Stack(
          children: [
            Container(
              width: 87.5.w,
              height: 18.h,
              margin: EdgeInsets.only(top: 15.h),
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: [
                  const Color(0xFF0054FF).withOpacity(0.3),
                  const Color(0xFF769CE9).withOpacity(0)
                ]),
              ),
            ),
            Text(
              title,
              style: TextStyle(
                fontSize: 16.sp,
                color: const Color(0xFF333333),
              ).phBold,
            ),
          ],
        )));
  }

  Widget _buildListItem(OperationalToolModel item) {
    return Container(
        width: double.infinity,
        margin: EdgeInsets.symmetric(horizontal: 16.w),
        padding:
            EdgeInsetsDirectional.symmetric(horizontal: 16.w, vertical: 16.h),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.r),
          boxShadow: [
            BoxShadow(
              color: const Color(0xFF002AA9).withOpacity(0.03),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Row(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.r),
                  child: CachedNetworkImage(
                      imageUrl: item.image, width: 60, height: 60),
                ),
                SizedBox(width: 12.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(item.name,
                        style: TextStyle(
                                color: const Color(0xFF323640), fontSize: 15.sp)
                            .pfSemiBold),
                    GestureDetector(
                      onTap: () {
                        showDialog(
                            context: context,
                            builder: (_) {
                              return GestureDetector(
                                onLongPress: () {
                                  _saveImgAction(
                                      context, item.qr_code, item.name);
                                },
                                child: Center(
                                  child: CachedNetworkImage(
                                    imageUrl: item.qr_code,
                                    width: 200.w,
                                    height: 200.h,
                                  ),
                                ),
                              );
                            });
                      },
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        alignment: Alignment.center,
                        height: 30.h,
                        child: Text('小程序码',
                            style: TextStyle(
                                    decorationColor: const Color(0xFF0054FF),
                                    decoration: TextDecoration.underline,
                                    fontSize: 12.sp,
                                    color: const Color(0xFF0054FF))
                                .pfRegular),
                      ),
                    ),
                  ],
                ),
                const Expanded(child: SizedBox()),
                if (item.meta != null && item.meta!.isNotEmpty)
                  GestureDetector(
                    onTap: () {
                      if (item.type == 1) {
                        NavigatorUtils.push(
                            context, CommonRouter.pdfPreviewPage,
                            arguments: {
                              'title': item.name,
                              'filePath': item.meta,
                            });
                      } else if (item.type == 2) {
                        String userName = item.meta!.split('@').first;
                        String path = item.meta!.split('@').last;
                        WeChatService().launchWeChatMiniProgram(
                            userName: userName, path: path);
                      }
                    },
                    child: Container(
                      padding: EdgeInsetsDirectional.symmetric(
                          horizontal: 20.w, vertical: 7.h),
                      decoration: BoxDecoration(
                        color: const Color(0xFF0054FF),
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                      child: Text('查看',
                          style: TextStyle(
                                  color: const Color(0xFFFFFFFF),
                                  fontSize: 12.sp)
                              .pfMedium),
                    ),
                  ),
              ],
            ),
            SizedBox(height: 12.h),
            Container(
              width: 311.w,
              height: 60.h,
              padding: EdgeInsetsDirectional.symmetric(
                  horizontal: 20.w, vertical: 10.h),
              decoration: BoxDecoration(
                  color: const Color(0xFFF7F8FD),
                  borderRadius: BorderRadius.circular(10.r)),
              child: Text(item.intro,
                  style:
                      TextStyle(color: const Color(0xFF8B90A0), fontSize: 13.sp)
                          .pfRegular),
            ),
          ],
        ));
  }
}
