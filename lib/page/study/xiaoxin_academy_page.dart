import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/navigator_push_utils.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/study/xiaoxin_menu_model.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/campus_training_router.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/routers/study_router.dart';
import 'package:npemployee/widget/study/gradient_icon_item.dart';
import 'package:npemployee/widget/study/simple_icon_item.dart';
import 'package:tap_debouncer/tap_debouncer.dart';

// Page for 小新学院

class XiaoxinAcademyPage extends StatefulWidget {
  const XiaoxinAcademyPage({super.key});

  @override
  State<XiaoxinAcademyPage> createState() => _XiaoxinAcademyPageState();
}

class _XiaoxinAcademyPageState extends State<XiaoxinAcademyPage> {
  List<XiaoxinSchoolModel> menus = [];

  void _getMenuList() {
    UserServiceProvider().getXiaoxinSchoolMenuList(
      cacheCallBack: (value) {
        _formatMenuListData(value, true);
      },
      successCallBack: (value) {
        _formatMenuListData(value, false);
      },
      errorCallBack: (value) {
        ToastUtils.show(value.msg);
      },
    );
  }

  _formatMenuListData(ResultData? value, bool isCache) {
    menus.clear();
    for (var e in value?.data) {
      List<XiaoxinMenusModel>? models = [];
      for (var e in e['menus'] ?? []) {
        models.add(XiaoxinMenusModel(
            id: e['id'],
            icon: e['icon'],
            jump: e['jump'],
            name: e['name'],
            sort: e['sort'],
            style: e['style']));
      }

      models.sort((a, b) => a.sort.compareTo(b.sort));

      XiaoxinSchoolModel schoolModel = XiaoxinSchoolModel(
        id: e['title']['id'],
        name: e['title']['name'],
        sort: e['title']['sort'],
        menus: models,
      );

      menus.add(schoolModel);
      menus.sort((a, b) => a.sort.compareTo(b.sort));
    }

    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _subscriptionTabChangeBloc() {
    BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.change && state.page == 99) {
        print("-- 进入小心学院页面 ${state.page}");
        _getMenuList();
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _getMenuList();
    _subscriptionTabChangeBloc();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...menus.map((item) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (item.menus.isNotEmpty) _buildGroupTitle(item.name, 30),
                item.id == 1
                    ? _buildGradientIconGrid(context, items: [
                        ...item.menus.map((e) {
                          return TapDebouncer(onTap: () async {
                            if (await UserServiceProvider()
                                .checkUserPermission(e.id)) {
                              NavigatorPushUtils.to(context, e.jump, e.style,
                                  () {
                                _getMenuList();
                              });
                              //                         NavigatorUtils.push(context, StudyRouter.answerQuestion, arguments: {
                              //   'paperId': 1,
                              //   'paperStatus': 'READY',
                              //   'limitTime': 60000,
                              // }).then((value) {

                              // });
                            }
                          }, builder: (_, TapDebouncerFunc? onTap) {
                            return GradientIconItem(
                              title: e.name,
                              svgPath: e.icon,
                              startColor:
                                  '${e.style?.first['value']}'.toColor(),
                              endColor: '${e.style?[1]['value']}'.toColor(),
                              onTap: onTap,
                            );
                          });
                        }),
                      ])
                    : _buildSimpleIconGrid(context, items: [
                        ...item.menus.map((e) {
                          return TapDebouncer(onTap: () async {
                            if (await UserServiceProvider()
                                .checkUserPermission(e.id)) {
                              NavigatorPushUtils.to(context, e.jump, e.style,
                                  () {
                                _getMenuList();
                              });
                            }
                          }, builder: (_, TapDebouncerFunc? onTap) {
                            return SimpleIconItem(
                                title: e.name,
                                svgPath: e.icon,
                                isDev: bool.tryParse(e.style?.first['value']),
                                onTap: onTap);
                          });
                        }),
                      ]),
              ],
            );
          }),

          // 企业相关 Section
          /* _buildGroupTitle('企业相关', 30),
          _buildGradientIconGrid(
            context,
            items: [
              GradientIconItem(
                title: '企业文化',
                svgPath: 'assets/svg/study/company_culture.svg',
                startColor: const Color(0xFFF3FFF4),
                endColor: const Color(0xFFF8F8F8),
                onTap: () =>
                    NavigatorUtils.push(context, StudyRouter.companyCulture),
              ),
              GradientIconItem(
                title: '年会视频',
                svgPath: 'assets/svg/study/annual_video.svg',
                startColor: const Color(0xFFEDF4FF),
                endColor: const Color(0xFFF8F8F8),
                onTap: () => NavigatorUtils.push(
                    context, StudyRouter.annualMeetingVideo,
                    arguments: {'title': '年会视频'}),
              ),
              GradientIconItem(
                title: '年会PPT',
                svgPath: 'assets/svg/study/annual_ppt.svg',
                startColor: const Color(0xFFFFEEEE),
                endColor: const Color(0xFFF8F8F8),
                onTap: () => NavigatorUtils.push(
                    context, StudyRouter.annualMeetingPPT,
                    arguments: {'title': '年会PPT'}),
              ),
              GradientIconItem(
                title: '知识竞赛',
                svgPath: 'assets/svg/study/knowledge_competition.svg',
                startColor: const Color(0xFFFFEDDF),
                endColor: const Color(0xFFF8F8F8),
                onTap: () => NavigatorUtils.push(
                    context, StudyRouter.knowledgeContest,
                    arguments: {'title': '知识竞赛'}),
              ),
            ],
          ),

          // 校区经营 Section (uses SimpleIconItem)
          _buildGroupTitle('校区经营', 45),
          _buildSimpleIconGrid(
            context,
            items: [
              SimpleIconItem(
                title: '校区培训',
                svgPath: 'assets/svg/study/campus_training.svg',
                isDev: false,
                onTap: () => NavigatorUtils.push(
                    context, CampusTrainingRouter.campusTrainingPage,
                    arguments: {'title': '校区培训'}),
              ),
              SimpleIconItem(
                title: '推广加盟',
                svgPath: 'assets/svg/study/promotion.svg',
                isDev: false,
                onTap: () => print('推广加盟 tapped'),
              ),
              SimpleIconItem(
                title: '成功案例',
                svgPath: 'assets/svg/study/success_cases.svg',
                isDev: true,
                onTap: () => print('成功案例 tapped'),
              ),
              SimpleIconItem(
                title: '地面增长',
                svgPath: 'assets/svg/study/ground_growth.svg',
                isDev: true,
                onTap: () => print('地面增长 tapped'),
              ),
              SimpleIconItem(
                title: '学员服务',
                svgPath: 'assets/svg/study/student_services.svg',
                isDev: true,
                onTap: () => print('学员服务 tapped'),
              ),
            ],
          ),

          // 个人提升 Section (uses SimpleIconItem)
          _buildGroupTitle('个人提升', 25),
          _buildSimpleIconGrid(
            context,
            items: [
              SimpleIconItem(
                title: '能力提升',
                svgPath: 'assets/svg/study/capability_up.svg',
                isDev: false,
                onTap: () => NavigatorUtils.push(
                    context, StudyRouter.capabilityUpgrading,
                    arguments: {'title': '能力提升'}),
              ),
              SimpleIconItem(
                title: '运营工具',
                svgPath: 'assets/svg/study/operational_tool.svg',
                isDev: false,
                onTap: () => NavigatorUtils.push(
                    context, StudyRouter.operationalTool,
                    arguments: {'title': '运营工具'}),
              ),
              SimpleIconItem(
                title: '常见问题',
                svgPath: 'assets/svg/study/faq.svg',
                isDev: false,
                onTap: () => print('常见问题 tapped'),
              ),
              SimpleIconItem(
                title: 'PS基础',
                svgPath: 'assets/svg/study/ps.svg',
                isDev: true,
                onTap: () => print('PS基础 tapped'),
              ),
              SimpleIconItem(
                title: 'Office小技巧',
                svgPath: 'assets/svg/study/office.svg',
                isDev: true,
                onTap: () => print('Office小技巧 tapped'),
              ),
            ],
          ), */
        ],
      ),
    );
  }

  Widget _buildGroupTitle(String title, double topPadding) {
    return Padding(
      padding: EdgeInsets.only(top: topPadding, left: 0),
      child: Text(
        title,
        style: TextStyle(fontSize: 16.sp, color: AppTheme.colorBlackTitle)
            .pfSemiBold,
      ),
    );
  }

  // Grid layout for GradientIconItems
  Widget _buildGradientIconGrid(BuildContext context,
      {required List<Widget> items}) {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: MediaQuery.removePadding(
          removeTop: true,
          context: context,
          child: GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: items.length,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 80 / 85.5,
            ),
            itemBuilder: (context, index) {
              return items[index];
            },
          )),
    );
  }

  Widget _buildSimpleIconGrid(BuildContext context,
      {required List<Widget> items}) {
    return Padding(
      padding: const EdgeInsets.only(top: 0, left: 0),
      child: Wrap(
        spacing: 0,
        runSpacing: 7,
        children: items,
      ),
    );

    // Get the screen width using the context
    /* final double screenWidth = MediaQuery.of(context).size.width;

    // Calculate available space for middle spacing
    final double totalHorizontalPadding = 46; // 23 pixels on each side
    final double totalSpacing =
        (items.length - 1) * 10; // Assuming 10px spacing between items
    final double availableWidth =
        screenWidth - totalHorizontalPadding - totalSpacing;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 23.0, vertical: 18.5),
      child: MediaQuery.removePadding(
          removeTop: true,
          context: context,
          child: GridView.builder(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            itemCount: items.length,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4, // 4 items per row
              crossAxisSpacing: 50, // Horizontal spacing between items
              mainAxisSpacing: 10, // Vertical spacing between rows
              childAspectRatio: availableWidth /
                  68, // Adjust the width for 4 items and 68px height
            ),
            itemBuilder: (context, index) {
              return items[index];
            },
          )),
    ); */
  }
}
