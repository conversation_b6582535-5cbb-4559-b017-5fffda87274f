import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/page/im/widgets/i_transimit_group_owner_select.dart';
import 'package:npemployee/routers/common_router.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_statelesswidget.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/core/tim_uikit_wide_modal_operation_key.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/controller/tim_uikit_chat_controller.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart' as tui;
import 'package:tencent_cloud_chat_uikit/ui/widgets/wide_popup.dart';

class ITimUikitGroupButtonArea extends TIMUIKitStatelessWidget {
  final String groupID;
  final TUIGroupProfileModel model;
  final sdkInstance = TIMUIKitCore.getSDKInstance();
  final coreInstance = TIMUIKitCore.getInstance();
  final TIMUIKitChatController _timuiKitChatController =
      TIMUIKitChatController();
  ITimUikitGroupButtonArea(this.groupID, this.model, {super.key});

  final _operationList = [
    {"label": TIM_t("清空消息"), "id": "clearHistory"},
    {"label": TIM_t("转让群主"), "id": "transimitOwner"},
    {"label": TIM_t("退出群聊"), "id": "quitGroup"},
    {"label": TIM_t("解散群组"), "id": "dismissGroup"}
  ];

  _clearHistory(BuildContext context, theme) async {
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;

    if (isDesktopScreen) {
      TUIKitWidePopup.showSecondaryConfirmDialog(
          operationKey: TUIKitWideModalOperationKey.confirmClearChatHistory,
          context: context,
          text: TIM_t("清空聊天记录"),
          theme: theme,
          onCancel: () {},
          onConfirm: () async {
            if (PlatformUtils().isWeb) {
              final res = await sdkInstance
                  .getConversationManager()
                  .deleteConversation(conversationID: "group_$groupID");
              if (res.code == 0) {
                _timuiKitChatController.clearHistory(groupID);
              }
            } else {
              final res = await sdkInstance
                  .getMessageManager()
                  .clearGroupHistoryMessage(groupID: groupID);
              if (res.code == 0) {
                _timuiKitChatController.clearHistory(groupID);
              }
            }
          });
    } else {
      showCupertinoModalPopup<String>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoActionSheet(
            title: Text(
              TIM_t("清空聊天记录"),
              style: TextStyle(color: '81848B'.toColor(), fontSize: 14.sp)
                  .pfMedium,
            ),
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(
                  context,
                );
              },
              isDefaultAction: false,
              child: Text(
                TIM_t("取消"),
                style: TextStyle(color: AppTheme.colorBlue, fontSize: 16.sp)
                    .pfMedium,
              ),
            ),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () async {
                  Navigator.pop(
                    context,
                  );
                  if (PlatformUtils().isWeb) {
                    final res = await sdkInstance
                        .getConversationManager()
                        .deleteConversation(conversationID: "group_$groupID");
                    if (res.code == 0) {
                      _timuiKitChatController.clearHistory(groupID);
                    }
                  } else {
                    final res = await sdkInstance
                        .getMessageManager()
                        .clearGroupHistoryMessage(groupID: groupID);
                    if (res.code == 0) {
                      _timuiKitChatController.clearHistory(groupID);
                    }
                  }
                },
                isDefaultAction: false,
                child: Text(
                  TIM_t("确定"),
                  style: TextStyle(color: 'FA4141'.toColor(), fontSize: 16.sp)
                      .pfMedium,
                ),
              )
            ],
          );
        },
      );
    }
  }

  _quitGroup(BuildContext context, TUITheme theme) async {
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;

    if (isDesktopScreen) {
      TUIKitWidePopup.showSecondaryConfirmDialog(
          operationKey: TUIKitWideModalOperationKey.confirmExitGroup,
          context: context,
          text: TIM_t("退出后不会接收到此群聊消息"),
          theme: theme,
          onCancel: () {},
          onConfirm: () async {
            final res = await sdkInstance.quitGroup(groupID: groupID);
            if (res.code == 0) {
              final deleteConvRes = await sdkInstance
                  .getConversationManager()
                  .deleteConversation(conversationID: "group_$groupID");
              if (deleteConvRes.code == 0) {
                model.lifeCycle?.didLeaveGroup();
              }
            }
          });
    } else {
      if (model.groupInfo?.role ==
              GroupMemberRoleType.V2TIM_GROUP_MEMBER_ROLE_OWNER &&
          model.groupMemberList.length > 1) {
        ToastUtils.show("群主不能退出群组,请先转让群主身份");
        return;
      }
      showCupertinoModalPopup<String>(
        context: context,
        builder: (BuildContext _) {
          return CupertinoActionSheet(
            title: Text(
              TIM_t("退出后不会接收到此群聊消息"),
              style: TextStyle(color: '81848B'.toColor(), fontSize: 14.sp)
                  .pfMedium,
            ),
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(_);
              },
              isDefaultAction: false,
              child: Text(
                TIM_t("取消"),
                style: TextStyle(color: AppTheme.colorBlue, fontSize: 16.sp)
                    .pfMedium,
              ),
            ),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () async {
                  final navigatorContext = Navigator.of(_);
                  final res = await sdkInstance.quitGroup(groupID: groupID);
                  if (res.code == 0) {
                    final deleteConvRes = await sdkInstance
                        .getConversationManager()
                        .deleteConversation(conversationID: "group_$groupID");
                    if (deleteConvRes.code == 0) {
                      navigatorContext
                          .popUntil(ModalRoute.withName(CommonRouter.tabs));
                      model.lifeCycle?.didLeaveGroup();
                    } else {
                      ToastUtils.show(deleteConvRes.desc);
                      return;
                    }
                    onTIMCallback(TIMCallback(
                        type: TIMCallbackType.INFO,
                        infoRecommendText:
                            "${TIM_t("您已退出")}${model.groupInfo?.groupName}",
                        infoCode: 6661402));
                  } else {
                    ToastUtils.show(res.desc);
                  }
                },
                isDefaultAction: false,
                child: Text(
                  TIM_t("确定"),
                  style: TextStyle(color: 'FA4141'.toColor(), fontSize: 16.sp)
                      .pfMedium,
                ),
              )
            ],
          );
        },
      );
    }
  }

  _dismissGroup(BuildContext context, theme) async {
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;

    if (isDesktopScreen) {
      TUIKitWidePopup.showSecondaryConfirmDialog(
          operationKey: TUIKitWideModalOperationKey.confirmDisbandGroup,
          context: context,
          text: TIM_t("解散后不会接收到此群聊消息"),
          theme: theme,
          onCancel: () {},
          onConfirm: () async {
            final res = await sdkInstance.dismissGroup(groupID: groupID);
            if (res.code == 0) {
              await sdkInstance
                  .getConversationManager()
                  .deleteConversation(conversationID: "group_$groupID");
              model.lifeCycle?.didLeaveGroup();
            }
          });
    } else {
      showCupertinoModalPopup<String>(
        context: context,
        builder: (BuildContext context) {
          return CupertinoActionSheet(
            title: Text(TIM_t("解散后不会接收到此群聊消息")),
            cancelButton: CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(
                  context,
                );
              },
              child: Text(TIM_t("取消")),
              isDefaultAction: false,
            ),
            actions: [
              CupertinoActionSheetAction(
                onPressed: () async {
                  final res = await sdkInstance.dismissGroup(groupID: groupID);
                  if (res.code == 0) {
                    await sdkInstance
                        .getConversationManager()
                        .deleteConversation(conversationID: "group_$groupID");
                    model.lifeCycle?.didLeaveGroup();
                  }
                },
                child: Text(
                  TIM_t("确定"),
                  style: TextStyle(color: theme.cautionColor),
                ),
                isDefaultAction: false,
              )
            ],
          );
        },
      );
    }
  }

  _transmitOwner(BuildContext context, String groupID) async {
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;

    if (isDesktopScreen) {
      TUIKitWidePopup.showPopupWindow(
        operationKey: TUIKitWideModalOperationKey.setAdmins,
        context: context,
        title: TIM_t("转让群主"),
        width: MediaQuery.of(context).size.width * 0.5,
        height: MediaQuery.of(context).size.height * 0.8,
        onSubmit: () {
          // selectNewGroupOwnerKey.currentState?.onSubmit();
        },
        child: (onClose) => SelectNewGroupOwner(
          model: model,
          // key: selectNewGroupOwnerKey,
          groupID: groupID,
          onSelectedMember: (selectedMember) async {
            if (selectedMember.isNotEmpty) {
              final userID = selectedMember.first.userID;
              await sdkInstance
                  .getGroupManager()
                  .transferGroupOwner(groupID: groupID, userID: userID);
            }
          },
        ),
      );
    } else {
      final navigatorContext = Navigator.of(context);
      List<V2TimGroupMemberFullInfo>? selectedMember = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ITransimitGroupOwnerSelect(
            model: model,
            groupID: groupID,
          ),
        ),
      );
      if (selectedMember != null) {
        final userID = selectedMember.first.userID;
        V2TimCallback res = await sdkInstance
            .getGroupManager()
            .transferGroupOwner(groupID: groupID, userID: userID);
        if (res.code != 0) {
          ToastUtils.show(res.desc);
          return;
        }
        navigatorContext.pop();
      }
    }
  }

  List<Widget> _renderGroupOperation(
      BuildContext context, TUITheme theme, bool isOwner, String groupType) {
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;
    return _operationList
        .where((element) {
          if (!isOwner) {
            return ["quitGroup", "clearHistory"].contains(element["id"]);
          } else {
            if (groupType == "Work") {
              return ["clearHistory", "quitGroup", "transimitOwner"]
                  .contains(element["id"]);
            }
            if (groupType != "Work") {
              return ["clearHistory", "dismissGroup", "transimitOwner"]
                  .contains(element["id"]);
            }
            return true;
          }
        })
        .map((e) => isDesktopScreen
            ? OutlinedButton(
                onPressed: () {
                  if (e["id"]! == "clearHistory") {
                    _clearHistory(context, theme);
                  } else if (e["id"] == "quitGroup") {
                    _quitGroup(context, theme);
                  } else if (e["id"] == "dismissGroup") {
                    _dismissGroup(context, theme);
                  } else if (e["id"] == "transimitOwner") {
                    _transmitOwner(context, groupID);
                  }
                },
                child: Text(
                  e["label"]!,
                  style: TextStyle(color: theme.cautionColor),
                ))
            : InkWell(
                onTap: () {
                  if (e["id"]! == "clearHistory") {
                    _clearHistory(context, theme);
                  } else if (e["id"] == "quitGroup") {
                    _quitGroup(context, theme);
                  } else if (e["id"] == "dismissGroup") {
                    _dismissGroup(context, theme);
                  } else if (e["id"] == "transimitOwner") {
                    _transmitOwner(context, groupID);
                  }
                },
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 8.5.w),
                      alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(vertical: 16.h),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(
                                  e['id'] == 'clearHistory' ? 6.r : 0),
                              topRight: Radius.circular(
                                  e['id'] == 'clearHistory' ? 6.r : 0),
                              bottomLeft: Radius.circular(
                                  e['id'] == 'quitGroup' ? 6.r : 0),
                              bottomRight: Radius.circular(
                                  e['id'] == 'quitGroup' ? 6.r : 0))),
                      child: Text(
                        e["label"]!,
                        style: TextStyle(
                                color: 'FA4141'.toColor(), fontSize: 14.sp)
                            .pfMedium,
                      ),
                    ),
                    if (e['id'] != 'quitGroup')
                      Stack(
                        children: [
                          Container(
                            color: Colors.white,
                            height: 1,
                            margin: EdgeInsets.symmetric(horizontal: 8.5.w),
                          ),
                          Container(
                            color: '#ECEAEA'.toColor().withOpacity(0.5),
                            height: 1,
                            margin:
                                EdgeInsets.symmetric(horizontal: 16.w + 8.5.w),
                          ),
                        ],
                      )
                  ],
                ),
              ))
        .toList();
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final theme = value.theme;
    final groupInfo = model.groupInfo;

    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;
    if (isDesktopScreen) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Wrap(
          spacing: 28,
          children: [
            ..._renderGroupOperation(
                context,
                theme,
                groupInfo?.owner == coreInstance.loginUserInfo?.userID,
                groupInfo?.groupType ?? "")
          ],
        ),
      );
    }

    return Column(
      children: [
        ..._renderGroupOperation(
            context,
            theme,
            groupInfo?.owner == coreInstance.loginUserInfo?.userID,
            groupInfo?.groupType ?? "")
      ],
    );
  }
}
