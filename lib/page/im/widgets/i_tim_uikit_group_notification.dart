import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/toast_utils.dart';
import 'package:npemployee/widget/common_nav.dart';
import 'package:provider/provider.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_base.dart';
import 'package:tencent_cloud_chat_uikit/base_widgets/tim_ui_kit_state.dart';
import 'package:tencent_cloud_chat_uikit/business_logic/separate_models/tui_group_profile_model.dart';
import 'package:tencent_cloud_chat_uikit/data_services/group/group_services.dart';
import 'package:tencent_cloud_chat_uikit/data_services/services_locatar.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart' as tui;

class ITimUikitGroupNotification extends StatefulWidget {
  final bool isHavePermission;
  const ITimUikitGroupNotification({super.key, this.isHavePermission = false});

  @override
  State<ITimUikitGroupNotification> createState() =>
      _ITimUikitGroupNotificationState();
}

class _ITimUikitGroupNotificationState
    extends TIMUIKitState<ITimUikitGroupNotification> {
  bool isShowEditBox = false;
  final TextEditingController _controller = TextEditingController();

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;
    final isDesktopScreen =
        tui.TUIKitScreenUtils.getFormFactor(context) == tui.DeviceType.Desktop;

    final model = Provider.of<TUIGroupProfileModel>(context);
    final String notification = (model.groupInfo?.notification != null &&
            model.groupInfo!.notification!.isNotEmpty)
        ? model.groupInfo!.notification!
        : TIM_t("暂无群公告");

    _setGroupNotification() async {
      setState(() {
        isShowEditBox = false;
      });
      final notification = _controller.text;
      await model.setGroupNotification(notification);
    }

    return Column(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          margin: EdgeInsets.symmetric(horizontal: 8.5.w),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: !widget.isHavePermission
                  ? BorderRadius.circular(6.r)
                  : BorderRadius.only(
                      topLeft: Radius.circular(6.r),
                      topRight: Radius.circular(6.r))),
          child: InkWell(
            onTap: !widget.isHavePermission
                ? null
                : (() {
                    final isDesktopScreen =
                        tui.TUIKitScreenUtils.getFormFactor(context) ==
                            tui.DeviceType.Desktop;
                    if (!isDesktopScreen) {
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) =>
                                  IGroupProfileNotificationPage(
                                      model: model,
                                      notification: notification)));
                    } else {
                      setState(() {
                        isShowEditBox = !isShowEditBox;
                        if (isShowEditBox) {
                          _controller.text = notification;
                        }
                      });
                    }
                  }),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Expanded(
                      child: Text(
                        TIM_t("群公告"),
                        style: TextStyle(
                                color: '000000'.toColor(),
                                fontSize: isDesktopScreen ? 14 : 14.sp)
                            .pfMedium,
                      ),
                    ),
                    if (widget.isHavePermission)
                      AnimatedRotation(
                        turns: isShowEditBox ? 0.25 : 0,
                        duration: const Duration(milliseconds: 200),
                        child: Image.asset('assets/png/arrow_right.png',
                            width: 10.w),
                      )
                  ],
                ),
                if (!isShowEditBox)
                  Padding(
                    padding: EdgeInsets.only(top: isDesktopScreen ? 4 : 0),
                    child: SelectableText(notification,
                        // overflow: isDesktopScreen ? null : TextOverflow.ellipsis,
                        // softWrap: true,
                        style: TextStyle(
                                color: '81848B'.toColor(), fontSize: 12.sp)
                            .pfRegular),
                  ),
                if (isShowEditBox)
                  Container(
                    margin: const EdgeInsets.only(top: 10, bottom: 10),
                    // height: 150,
                    child: TextField(
                        minLines: 1,
                        maxLines: 6,
                        controller: _controller,
                        keyboardType: TextInputType.multiline,
                        autofocus: true,
                        style: const TextStyle(fontSize: 13),
                        decoration: InputDecoration(
                            contentPadding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(5.0),
                                borderSide: BorderSide(
                                  color: theme.weakDividerColor ?? Colors.grey,
                                )),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(5.0),
                              borderSide: BorderSide(
                                color: theme.weakDividerColor ?? Colors.grey,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              //选中时外边框颜色
                              borderRadius: BorderRadius.circular(5.0),
                              borderSide: BorderSide(
                                color: theme.weakTextColor ?? Colors.grey,
                              ),
                            ),
                            hintStyle: const TextStyle(
                              color: Color(0xFFAEA4A3),
                            ),
                            hintText: '')),
                  ),
                if (isShowEditBox)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      OutlinedButton(
                          onPressed: _setGroupNotification,
                          child: Text(
                            TIM_t("保存"),
                            style: TextStyle(
                                fontSize: 13, color: theme.primaryColor),
                          ))
                    ],
                  )
              ],
            ),
          ),
        ),
        if (widget.isHavePermission)
          Stack(
            children: [
              Container(
                color: Colors.white,
                height: 1,
                margin: EdgeInsets.symmetric(horizontal: 8.5.w),
              ),
              Container(
                color: '#ECEAEA'.toColor().withOpacity(0.5),
                height: 1,
                margin: EdgeInsets.symmetric(horizontal: 16.w + 8.5.w),
              ),
            ],
          ),
      ],
    );
  }
}

class IGroupProfileNotificationPage extends StatefulWidget {
  final String notification;
  final TUIGroupProfileModel? model;
  final String? groupID;
  const IGroupProfileNotificationPage(
      {super.key, required this.notification, this.model, this.groupID});

  @override
  State<IGroupProfileNotificationPage> createState() =>
      _IGroupProfileNotificationPageState();
}

class _IGroupProfileNotificationPageState
    extends TIMUIKitState<IGroupProfileNotificationPage> {
  final TextEditingController _controller = TextEditingController();
  bool isUpdated = false;
  final GroupServices _groupServices = serviceLocator<GroupServices>();

  _setGroupNotification() async {
    final notification = _controller.text;
    if (widget.model == null) {
      final response = await _groupServices.setGroupInfo(
          info: V2TimGroupInfo.fromJson({
        "groupID": widget.groupID,
        "groupType": GroupType.Work,
        "notification": notification
      }));
      if (response.code != 0) {
        ToastUtils.show(response.desc);
      }
    } else {
      await widget.model!.setGroupNotification(notification);
    }
    setState(() {
      isUpdated = true;
    });
  }

  @override
  void initState() {
    _controller.text = widget.notification;
    super.initState();
  }

  @override
  Widget tuiBuild(BuildContext context, TUIKitBuildValue value) {
    final TUITheme theme = value.theme;

    return Scaffold(
      backgroundColor: '#F7F8FD'.toColor(),
      appBar: CommonNav(
        title: '群公告',
        rightWidget: [
          TextButton(
            onPressed: () {
              if (isUpdated) {
                setState(() {
                  isUpdated = false;
                });
              } else {
                _setGroupNotification();
              }
            },
            child: Text(
              isUpdated ? TIM_t("编辑") : TIM_t("发布"),
              style: TextStyle(
                color: '1458F5'.toColor(),
                fontSize: 15.sp,
              ).pfSemiBold,
            ),
          )
        ],
      )
      /* AppBar(
        title: Text(
          TIM_t("群公告"),
          style: TextStyle(color: theme.appbarTextColor, fontSize: 17),
        ),
        backgroundColor: theme.appbarBgColor ?? theme.primaryColor,
        shadowColor: theme.weakDividerColor,
        iconTheme: IconThemeData(
          color: theme.appbarTextColor,
        ),
        actions: [
          TextButton(
            onPressed: () {
              if (isUpdated) {
                setState(() {
                  isUpdated = false;
                });
              } else {
                _setGroupNotification();
              }
            },
            child: Text(
              isUpdated ? TIM_t("编辑") : TIM_t("完成"),
              style: TextStyle(
                color: theme.appbarTextColor,
                fontSize: 14,
              ),
            ),
          )
        ],
      ) */
      ,
      body: Container(
        height: ScreenUtil().screenHeight,
        color: Colors.white,
        margin: EdgeInsets.symmetric(horizontal: 11.w, vertical: 11.h),
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: TextField(
            readOnly: isUpdated,
            minLines: 1,
            style:
                TextStyle(color: '333333'.toColor(), fontSize: 14.sp).pfMedium,
            maxLines: 4,
            controller: _controller,
            keyboardType: TextInputType.multiline,
            autofocus: true,
            decoration: const InputDecoration(
                border: InputBorder.none,
                hintStyle: TextStyle(
                  // fontSize: 10,
                  color: Color(0xFFAEA4A3),
                ),
                hintText: '')),
      ),
    );
  }
}
