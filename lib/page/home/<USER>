import 'dart:async';
import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_refresh/easy_refresh.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marqueer/marqueer.dart';
import 'package:npemployee/Utils/date_time_utils.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/navigator_push_utils.dart';
import 'package:npemployee/Utils/validator_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/page/no_data_page.dart';
import 'package:npemployee/common/page/photo_browser.dart';
import 'package:npemployee/common/page/webview_screen_page.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/home/<USER>';
import 'package:npemployee/model/home/<USER>';
import 'package:npemployee/model/home/<USER>';
import 'package:npemployee/model/home/<USER>';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/home/<USER>/drag_bottom_dismiss_dialog.dart';
import 'package:npemployee/page/home/<USER>/video_page.dart';
import 'package:npemployee/pages/bluetooth_example_page.dart';
import 'package:npemployee/pages/location_example_page.dart';
import 'package:npemployee/pages/step_count_example_page.dart';
import 'package:npemployee/provider/home_color_bloc/home_color_state.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:npemployee/widget/home/<USER>';
import 'package:permission_handler/permission_handler.dart';
import 'package:video_player/video_player.dart';

import '../../Utils/camera_permission_manager.dart';
import '../../common/dialog/custom_dialog.dart';

class HomePage extends StatefulWidget {
  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  final PageController _pageController = PageController(); //新途径圈中tab切换
  final ScrollController _scrollController = ScrollController(); //
  int _currentTagIndex = 0;
  final PageController _topPageController = PageController(); //增长榜-新途径圈-同行朋友圈
  int _currentTopIndex = 0;
  List<CircleCategoryModel> _categorys = [];
  List<CircleBannerModel> _banners = [];
  List<CircleNotifyModel> _notifys = [];
  final List<GlobalKey> _tabKeyList = [];
  List<CircleBannerModel> _pops = [];

  int page = 1;
  int size = 10;
  bool hasMore = false;
  List<CirclePostModel> _posts = [];
  late EasyRefreshController _refreshController;

  StreamSubscription? _tabSubscription;
  StreamSubscription? _homeColorSubscription;
  HomeColorState _currentHomeColorState = HomeColorState.defaultState;

  void _autoScrollTabToVisible() {
    final RenderBox? tabRenderBox = _tabKeyList[_currentTagIndex]
        .currentContext
        ?.findRenderObject() as RenderBox?;
    if (tabRenderBox != null) {
      final tabPosition = tabRenderBox.localToGlobal(Offset.zero).dx;
      final tabWidth = tabRenderBox.size.width;
      final screenWidth = MediaQuery.of(context).size.width;
      final scrollOffset = _scrollController.offset;

      if (tabPosition < 0) {
        _scrollController.animateTo(scrollOffset + tabPosition - 16.w,
            duration: const Duration(milliseconds: 300), curve: Curves.ease);
      } else if (tabPosition + tabWidth > screenWidth) {
        _scrollController.animateTo(
            scrollOffset + (tabPosition + tabWidth - screenWidth) + 16.w,
            duration: const Duration(milliseconds: 300),
            curve: Curves.ease);
      }
    }
  }

  // 检查相机权限并处理
  Future<void> _handleCameraPermission() async {
    bool hasPermission = await PermissionManager.checkCameraPermission();
    if (hasPermission) {
      // 有权限，打开相机
      NavigatorUtils.push(context, MineRouter.qrcodeScanPage);
    } else {
      // 请求权限
      bool granted = await PermissionManager.requestCameraPermission();

      if (granted) {
        // 获得权限，打开相机
        NavigatorUtils.push(context, MineRouter.qrcodeScanPage);
      } else {
        // 判断是否永久拒绝
        bool isPermanentlyDenied =
            await PermissionManager.isPermanentlyDenied();

        if (isPermanentlyDenied) {
          _showSettingsDialog();
        } else {
          _showDeniedDialog();
        }
      }
    }
  }

  // 显示引导用户去设置的对话框
  void _showSettingsDialog() {
    PermissionAlertDialog.show(
      context: context,
      title: '需要相机权限',
      content: '请设置中允许访问相机',
      confirmText: '去设置',
      cancelText: '取消',
      onConfirm: () {
        openAppSettings();
      },
    );
  }

  // 显示权限被拒绝的对话框
  void _showDeniedDialog() {
    PermissionAlertDialog.show(
      context: context,
      title: '相册未授权',
      content: '请前往【设置】打开相机权限',
      confirmText: '我知道了',
    );
  }

  @override
  void initState() {
    _refreshController = EasyRefreshController(
        controlFinishRefresh: true, controlFinishLoad: true);

    super.initState();

    ///监听页面切换
    _subscriptionTabChangeBloc();

    ///监听Home页面颜色变化
    _subscriptionHomeColorBloc();
  }

  @override
  void dispose() {
    _tabSubscription?.cancel();
    _homeColorSubscription?.cancel();
    _refreshController.dispose();
    super.dispose();
  }

  void _subscriptionTabChangeBloc() {
    _tabSubscription?.cancel();
    _tabSubscription = BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.change && state.page == 0) {
        if (_currentTopIndex == 1) {
          _getPersonCircleData();
        } else {
          setState(() {});
        }
      }
    });
  }

  void _subscriptionHomeColorBloc() {
    _homeColorSubscription?.cancel();
    _homeColorSubscription = BlocManager().homeColorBloc.stream.listen((state) {
      setState(() {
        _currentHomeColorState = state;
      });
    });
  }

  void _getPersonCircleData() {
    if (_currentTopIndex == 1) {
      _getBanners();
      _getNotifys();
      _getCircleCategory();
      _getPops();
    }
  }

  void _getBanners() {
    UserServiceProvider().getBannerList(
        cacheCallBack: (data) {},
        successCallBack: (data) {
          _formatBannersData(data, false);
        },
        errorCallBack: (err) {});
  }

  void _formatBannersData(ResultData data, bool isCache) {
    _banners.clear();
    for (var element in data.data ?? []) {
      _banners.add(CircleBannerModel.fromJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _getPops() {
    UserServiceProvider().getCirclePopList(cacheCallBack: (data) {
      _formatPopDatas(data, true);
    }, successCallBack: (data) {
      _formatPopDatas(data, false);
    }, errorCallBack: (data) {
      debugPrint('----------- 获取悬浮弹窗失败');
    });
  }

  void _formatPopDatas(ResultData? data, bool isCache) {
    _pops.clear();
    for (var element in data?.data ?? []) {
      _pops.add(CircleBannerModel.fromJson(element));
    }
    if (!isCache) {
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _getNotifys() {
    UserServiceProvider().getCircleNotifyList(
        cacheCallBack: (data) {},
        successCallBack: (data) {
          _formatNotifysData(data);
        },
        errorCallBack: (err) {
          debugPrint('----- 获取新途径圈通知失败');
        });
  }

  void _formatNotifysData(ResultData data) {
    _notifys.clear();
    for (var element in data.data ?? []) {
      if (element['hidden'] == false) {
        _notifys.add(CircleNotifyModel.fromJson(element));
      }
    }
    setState(() {});
  }

  void _getCircleCategory() {
    UserServiceProvider().getCircleCategory(
      cacheCallBack: (data) {
        _formatCircleCategory(data, true);
      },
      successCallBack: (data) {
        _formatCircleCategory(data, false);
      },
      errorCallBack: (data) {},
    );
  }

  void _formatCircleCategory(ResultData data, bool isCache) {
    _categorys.clear();
    _categorys.add(CircleCategoryModel(null, '全部', -1));
    for (var element in data.data ?? []) {
      _categorys.add(CircleCategoryModel.fromJson(element));
    }
    _tabKeyList.clear();
    _tabKeyList.addAll(_categorys.map((e) => GlobalKey()));
    if (!isCache) {
      _getCirclePosts(_categorys[_currentTagIndex].id);
      if (mounted) {
        setState(() {});
      }
    }
  }

  void _getCirclePosts(int? category_id, {bool isRefresh = true}) {
    page = isRefresh ? 1 : page + 1;

    UserServiceProvider().getCirclePosts(
      category_id: category_id,
      page: page,
      size: size,
      cacheCallBack: (data) {},
      successCallBack: (data) {
        _formatCirclePostsData(data, false, isRefresh);
      },
      errorCallBack: (data) {
        if (isRefresh) {
          _refreshController.finishRefresh(IndicatorResult.fail);
        } else {
          _refreshController.finishLoad(IndicatorResult.fail);
        }
      },
    );
  }

  void _formatCirclePostsData(ResultData data, bool isCache, bool isRefresh) {
    hasMore = ValidatorUtils.listHasMore(data.count, page, size);
    if (isRefresh) {
      _refreshController.finishRefresh(IndicatorResult.success);
      _posts.clear();
    } else {
      _refreshController.finishLoad(
          hasMore ? IndicatorResult.success : IndicatorResult.noMore);
    }

    for (var element in data.data ?? []) {
      _posts.add(CirclePostModel.fromJson(element));
    }

    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.zero,
          child: AppBar(
            backgroundColor: _currentTopIndex == 0
                ? _currentHomeColorState.statusBarColor
                : Colors.transparent,
            elevation: 0,
            systemOverlayStyle: SystemUiOverlayStyle(
              statusBarColor: _currentTopIndex == 0
                  ? _currentHomeColorState.statusBarColor
                  : Colors.transparent,
              statusBarIconBrightness: _currentTopIndex == 0
                  ? _currentHomeColorState.iconBrightness
                  : Brightness.dark,
              statusBarBrightness: Platform.isAndroid
                  ? (_currentTopIndex == 0
                      ? (_currentHomeColorState.iconBrightness ==
                              Brightness.dark
                          ? Brightness.light
                          : Brightness.dark)
                      : Brightness.light)
                  : null,
            ),
          ),
        ),
        body: Stack(
          children: [
            Container(
              width: ScreenUtil().screenWidth,
              height: 202.h,
              decoration: BoxDecoration(
                  color: _currentTopIndex == 0
                      ? _currentHomeColorState.statusBarColor
                      : null,
                  gradient: _currentTopIndex == 1 || _currentTopIndex == 2
                      ? LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [Color(0xFFD6E8FA), Color(0xFFFFFFFF)])
                      : null),
            ),
            Column(
              children: [
                _topTabView(),
                Expanded(
                    child: PageView(
                  controller: _topPageController,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    WebviewScreenPage(
                        needNav: false,
                        url: AppInfo().growthChartUrl,
                        keepAlive: true,
                        enableLongPress: false,
                        gradient: const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [Color(0xFFE5F1FC), Color(0xFFFFFFFF)])),
                    NestedScrollView(
                      physics: const NeverScrollableScrollPhysics(),
                      headerSliverBuilder: (context, innerBoxIsScrolled) => [
                        SliverToBoxAdapter(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (_banners.isNotEmpty)
                                BannerView(banners: _banners),
                              if (_notifys.isNotEmpty) SizedBox(height: 13.h),
                              if (_notifys.isNotEmpty) _messageView(),
                            ],
                          ),
                        ),
                      ],
                      body: NotificationListener<ScrollNotification>(
                          onNotification: (ScrollNotification notification) {
                            if (notification is ScrollEndNotification) {
                              if (_pageController.position.pixels ==
                                  _pageController.position.maxScrollExtent) {}
                            }
                            return true;
                          },
                          child: Column(
                            children: [
                              _tabView(),
                              Expanded(
                                  child: Container(
                                color: Colors.transparent,
                                child: EasyRefresh.builder(
                                    controller: _refreshController,
                                    onRefresh: () async {
                                      _getCirclePosts(
                                          _categorys[_currentTagIndex].id,
                                          isRefresh: true);
                                    },
                                    onLoad: !hasMore
                                        ? null
                                        : () async {
                                            _getCirclePosts(
                                                _categorys[_currentTagIndex].id,
                                                isRefresh: false);
                                          },
                                    childBuilder: (_, physics) {
                                      return PageView.builder(
                                          controller: _pageController,
                                          itemCount: _categorys.length,
                                          onPageChanged: (value) {
                                            setState(() {
                                              _currentTagIndex = value;
                                            });
                                            _autoScrollTabToVisible();
                                            _getCirclePosts(
                                                _categorys[_currentTagIndex].id,
                                                isRefresh: true);
                                          },
                                          itemBuilder: (_, index) {
                                            return ContentPage(
                                              posts: _posts,
                                              categorys: _categorys,
                                              physics: physics,
                                            );
                                          });
                                    }),
                              ))
                            ],
                          )),
                    ),
                    WebviewScreenPage(
                        needNav: false,
                        keepAlive: true,
                        enableLongPress: false,
                        url: AppInfo().friendCircleUrl,
                        gradient: const LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [Color(0xFFE5F1FC), Color(0xFFFFFFFF)])),
                  ],
                )),
              ],
            ),
            if (_pops.isNotEmpty)
              Positioned(
                  bottom: 24.h,
                  right: 16.w,
                  child: GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      NavigatorPushUtils.to(
                          context, _pops.first.jump, _pops.first.style, () {
                        _getPersonCircleData();
                      });
                    },
                    child: CachedNetworkImage(
                      imageUrl: _pops.first.images.first.meta,
                      width: _pops.first.images.first.width.toDouble(),
                      height: _pops.first.images.first.height.toDouble(),
                    ),
                  ))
          ],
        ));
  }

  Widget _topTabView() {
    return Container(
      height: 88.h - ScreenUtil().statusBarHeight,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          ...['增长榜', '新途径圈', '同行朋友圈'].map((e) {
            return Padding(
                padding: EdgeInsets.only(left: e == '增长榜' ? 16.w : 20.w),
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    setState(() {
                      if (_currentTopIndex !=
                          ['增长榜', '新途径圈', '同行朋友圈'].indexOf(e)) {
                        _currentTopIndex = ['增长榜', '新途径圈', '同行朋友圈'].indexOf(e);
                        _currentTagIndex = 0;
                        _getPersonCircleData();
                      }
                    });
                    _topPageController.animateToPage(_currentTopIndex,
                        duration: const Duration(milliseconds: 200),
                        curve: Curves.ease);
                    if (_currentTopIndex == 1) {
                      if (_categorys.isEmpty) {
                        _categorys.add(CircleCategoryModel(null, '全部', -1));
                        _tabKeyList.addAll(_categorys.map((e) => GlobalKey()));
                      }
                      _getCirclePosts(_categorys[_currentTagIndex].id);
                    }
                  },
                  child: Text(e,
                      style: e == ['增长榜', '新途径圈', '同行朋友圈'][_currentTopIndex]
                          ? TextStyle(
                                  color: _currentTopIndex == 0 &&
                                          !_currentHomeColorState.isDefault
                                      ? _currentHomeColorState.textColor
                                      : AppTheme.colorBlackTitle,
                                  fontSize: 22.sp)
                              .pfSemiBold
                          : TextStyle(
                                  color: _currentTopIndex == 0 &&
                                          !_currentHomeColorState.isDefault
                                      ? _currentHomeColorState.textColor
                                          .withOpacity(0.7)
                                      : const Color(0xFF878F97),
                                  fontSize: 18.sp)
                              .pfMedium),
                ));
          }),
          const Expanded(child: SizedBox()),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () async {
              var cameraStatus = await Permission.camera.status;
              if (!cameraStatus.isGranted) {
                if (mounted) {
                  if (Platform.isAndroid) {
                    showDialog(
                      context: context,
                      builder: (context) => CustomDialog(
                        title: "提示",
                        content: "扫描二维码需要相机权限",
                        cancelButtonText: "取消",
                        confirmButtonText: "确定",
                        cancelButtonColor: AppTheme.colorButtonGrey,
                        confirmButtonColor: AppTheme.colorBlue,
                        onCancel: () {
                          Navigator.of(context).pop();
                        },
                        onConfirm: () async {
                          Navigator.of(context).pop();
                          _handleCameraPermission();
                        },
                      ),
                    );
                  } else {
                    _handleCameraPermission();
                  }
                }
              } else {
                _handleCameraPermission();
              }
            },
            child: Container(
              padding: EdgeInsets.fromLTRB(8, 8, 16.w, 0),
              child: Image.asset(
                'assets/png/home/<USER>',
                width: 20,
                height: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _messageView() {
    return Container(
      height: 30.h,
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Row(
        children: [
          Image.asset('assets/png/home/<USER>',
              width: 30, height: 30),
          SizedBox(width: 6.w),
          Expanded(
              child: Stack(
            alignment: Alignment.center,
            children: [
              SizedBox(
                width: double.infinity,
                child: Image.asset(
                  'assets/png/home/<USER>',
                  fit: BoxFit.fitWidth,
                  width: 306.5.w,
                ),
              ),
              Container(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Marqueer.builder(
                    itemCount: _notifys.length * 1000,
                    interaction: true,
                    restartAfterInteractionDuration:
                        const Duration(milliseconds: 400),
                    scrollablePointerIgnoring: true,
                    itemBuilder: (context, index) {
                      int realIndex = index % _notifys.length;
                      return GestureDetector(
                          onTap: () {
                            NavigatorPushUtils.to(
                                context,
                                _notifys[realIndex].jump,
                                _notifys[realIndex].style, () {
                              _getPersonCircleData();
                            });
                          },
                          child: Padding(
                            padding: EdgeInsets.only(right: 16.w),
                            child: Row(
                              children: [
                                Text(
                                  _notifys[realIndex].content ?? '',
                                  style: TextStyle(
                                          fontSize: 14.sp,
                                          color: AppTheme.colorBlue)
                                      .pfRegular,
                                ),
                                SizedBox(width: 4.5.w),
                              ],
                            ),
                          ));
                    },
                  ))
            ],
          ))
        ],
      ),
    );
  }

  Widget _tabView() {
    return Container(
        padding: EdgeInsets.only(top: 16.h),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          controller: _scrollController,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              ..._categorys.map((e) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: HomeTabItem(
                      key: _tabKeyList[_categorys.indexOf(e)],
                      title: e.name,
                      isSelect: _currentTagIndex == _categorys.indexOf(e),
                      onTap: () {
                        setState(() {
                          _currentTagIndex = _categorys.indexOf(e);
                        });
                        _pageController.animateToPage(_currentTagIndex,
                            duration: const Duration(milliseconds: 300),
                            curve: Curves.ease);
                        _getCirclePosts(_categorys[_currentTagIndex].id);
                      }),
                );
              }),
            ],
          ),
        ));
  }
}

class ContentPage extends StatefulWidget {
  final List<CirclePostModel>? posts;
  final List<CircleCategoryModel> categorys;
  final ScrollPhysics? physics;
  const ContentPage(
      {super.key,
      required this.posts,
      required this.categorys,
      required this.physics});

  @override
  State<ContentPage> createState() => _ContentPageState();
}

class _ContentPageState extends State<ContentPage>
    with AutomaticKeepAliveClientMixin {
  List<CirclePostModel> _posts = [];
  ScrollPhysics? _physics;

  final Map<String, bool> isLandscapeCache = {}; //图片缓存

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _posts = widget.posts ?? [];
    _physics = widget.physics;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return _posts.isEmpty
        ? NoDataPage(physics: _physics)
        : MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: ListView.builder(
              physics: _physics,
              itemCount: _posts.length,
              itemBuilder: _itemBuilder,
            ));
  }

  Widget _itemBuilder(BuildContext c, int index) {
    CirclePostModel postModel = _posts[index];

    int categoryIndex =
        widget.categorys.indexWhere((e) => e.id == postModel.category_id);
    String? categoryStr;
    if (categoryIndex != -1) {
      categoryStr = widget.categorys[categoryIndex].name;
    }
    return Container(
        decoration: BoxDecoration(
            border: Border(
                bottom:
                    BorderSide(color: AppTheme.colorDivider, width: 0.5.h))),
        child: Padding(
          padding: EdgeInsets.fromLTRB(16.w, 12.h, 16.w, 0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  ClipRRect(
                    borderRadius: BorderRadius.circular(7.r),
                    child: CachedNetworkImage(
                        imageUrl: postModel.show_poster_avatar.isNotEmpty
                            ? postModel.show_poster_avatar
                            : postModel.poster.avatar,
                        width: 35,
                        height: 35),
                  ),
                  SizedBox(width: 7.5.h),
                  Text(
                      postModel.show_poster_name.isNotEmpty
                          ? postModel.show_poster_name
                          : postModel.poster.name,
                      style: TextStyle(
                              color: const Color(0xFF5F7093), fontSize: 15.sp)
                          .pfMedium)
                ],
              ),
              Container(
                margin: EdgeInsets.only(left: 60.w - 16.w),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(postModel.content,
                          style: TextStyle(
                                  color: const Color(0xFF161A1C),
                                  fontSize: 14.sp)
                              .pfRegular),
                      SizedBox(height: 8.h),
                      if (postModel.media.first.media_type == 'video')
                        VideoPostCard(post: postModel),
                      if (postModel.media.first.media_type == 'image')
                        postModel.media.length == 1
                            ? GestureDetector(
                                behavior: HitTestBehavior.opaque,
                                onTap: () {
                                  PhotoBrowser.show(context,
                                      data: postModel.media
                                          .map((e) => e.url)
                                          .toList(),
                                      index: index);
                                },
                                child: _getImage(postModel.media.first.url),
                              )
                            : GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                        crossAxisCount: 3,
                                        mainAxisSpacing: 2,
                                        crossAxisSpacing: 2),
                                itemCount: postModel.media.length,
                                itemBuilder: (context, index) {
                                  return GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () {
                                      PhotoBrowser.show(context,
                                          data: postModel.media
                                              .map((e) => e.url)
                                              .toList(),
                                          index: index);
                                    },
                                    child: CachedNetworkImage(
                                      imageUrl:
                                          postModel.media[index].url.isEmpty
                                              ? ValidatorUtils.testImageUrl
                                              : postModel.media[index].url,
                                      fit: BoxFit.cover,
                                    ),
                                  );
                                }),
                      SizedBox(height: 10.h),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            categoryStr == null
                                ? DateTimeUtils.formatCustomDate(
                                    postModel.show_post_time,
                                    'yyyy-MM-dd HH:mm')
                                : '$categoryStr | ${DateTimeUtils.formatCustomDate(postModel.show_post_time, 'yyyy-MM-dd HH:mm')}',
                            style: TextStyle(
                                    color: const Color(0xFFA4A4A4),
                                    fontSize: 11.sp)
                                .pfRegular,
                          ),
                          TextButton(
                              onPressed: () {
                                UserServiceProvider().getCirclePostLike(
                                    postModel.id, successCallBack: (data) {
                                  if (data.code == 0) {
                                    setState(() {
                                      postModel.liked = !postModel.liked;
                                      postModel.likes = postModel.liked
                                          ? postModel.likes + 1
                                          : postModel.likes - 1;
                                    });
                                  }
                                }, errorCallBack: (err) {
                                  EasyLoading.showError(err.msg);
                                });
                              },
                              child: Row(
                                children: [
                                  Image.asset(
                                      postModel.liked
                                          ? 'assets/png/home/<USER>'
                                          : 'assets/png/home/<USER>',
                                      width: 15,
                                      height: 15),
                                  SizedBox(width: 2.w),
                                  Text(
                                    '${postModel.likes}',
                                    style: TextStyle(
                                            color: const Color(0xFFA4A4A4),
                                            fontSize: 11.sp)
                                        .pfRegular,
                                  ),
                                ],
                              ))
                        ],
                      )
                    ]),
              ),
            ],
          ),
        ));
  }

  Widget _getImage(String imgUrl) {
    double size = (ScreenUtil().screenWidth - 60.w - 16.w - 4) / 3;
    if (isLandscapeCache.containsKey(imgUrl)) {
      if (isLandscapeCache[imgUrl]!) {
        return CachedNetworkImage(
            imageUrl: imgUrl, fit: BoxFit.fitHeight, height: size);
      }
      return CachedNetworkImage(
          imageUrl: imgUrl, fit: BoxFit.fitWidth, width: size);
    } else {
      return FutureBuilder(
          future: _isLandscape(imgUrl),
          builder: (_, snap) {
            if (snap.connectionState == ConnectionState.done) {
              final isLandscape = snap.data ?? false;
              if (isLandscape) {
                return CachedNetworkImage(
                    imageUrl: imgUrl, fit: BoxFit.fitHeight, height: size);
              }
              return CachedNetworkImage(
                  imageUrl: imgUrl, fit: BoxFit.fitWidth, width: size);
            }
            return Container();
          });
    }
  }

  // 判断图片是否为横向
  Future<bool> _isLandscape(String imageUrl) async {
    final Completer<bool> completer = Completer();
    final Image image = Image.network(imageUrl);
    image.image.resolve(ImageConfiguration()).addListener(
      ImageStreamListener((ImageInfo info, bool _) {
        final bool isLandscape = info.image.width > info.image.height;
        isLandscapeCache[imageUrl] = isLandscape;
        completer.complete(isLandscape);
      }),
    );

    return completer.future;
  }
}

class VideoPostCard extends StatefulWidget {
  final CirclePostModel post;

  VideoPostCard({required this.post});

  @override
  _VideoPostCardState createState() => _VideoPostCardState();
}

class _VideoPostCardState extends State<VideoPostCard> {
  late VideoPlayerController _controller;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _controller =
        VideoPlayerController.networkUrl(Uri.parse(widget.post.media.first.url))
          ..initialize().then((_) {
            setState(() {});
          });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _togglePlayPause() {
    Navigator.push(
      context,
      DragBottomDismissDialog(
        builder: (context) {
          return VideoPage(
            videoPlayerController: _controller,
            heroTag: widget.post.media.first.url,
          );
        },
      ),
    ).then((value) {});
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
              onTap: _togglePlayPause,
              child: Hero(
                tag: widget.post.media.first.url,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    _controller.value.isInitialized
                        ? AspectRatio(
                            aspectRatio: _controller.value.aspectRatio,
                            child: VideoPlayer(_controller),
                          )
                        : const Center(child: CircularProgressIndicator()),
                    if (!_isPlaying)
                      const Icon(
                        Icons.play_circle_fill,
                        color: Colors.white,
                        size: 50,
                      ),
                  ],
                ),
              )),
          // Add any additional content like captions or comments here
        ],
      ),
    );
  }
}

class BannerView extends StatefulWidget {
  final List<CircleBannerModel> banners;
  const BannerView({super.key, required this.banners});

  @override
  State<BannerView> createState() => _BannerViewState();
}

class _BannerViewState extends State<BannerView> {
  List<CircleBannerModel> _banners = [];
  Timer? _timer;
  final PageController _bannerController = PageController();
  int _currentBannerIndex = 0;
  int bannerAutoIndex = 0;

  void _startAutoScroll() {
    _timer?.cancel();
    _timer = Timer.periodic(Duration(seconds: 3), (timer) {
      if (_bannerController.hasClients) {
        bannerAutoIndex++;
        if (bannerAutoIndex >= 9999999) {
          bannerAutoIndex = 0;
        }
        _bannerController.animateToPage(
          bannerAutoIndex,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _banners = widget.banners;
  }

  @override
  void didUpdateWidget(covariant BannerView oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _timer?.cancel();
    _timer = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(16.w, _banners.isEmpty ? 0 : 16.h, 16.w, 0),
      height: 130.h,
      width: 343.w,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          PageView.builder(
            controller: _bannerController,
            itemCount: _banners.length * 1000,
            physics: _banners.length == 1
                ? const NeverScrollableScrollPhysics()
                : const PageScrollPhysics(),
            itemBuilder: (context, index) {
              final banner = _banners[index % _banners.length];
              final isPad = MediaQuery.of(context).size.width > 600;
              final imageUrl = banner.images
                  .firstWhere(
                    (element) => isPad
                        ? element.width == 1024 && element.height == 768
                        : element.width == 800 && element.height == 600,
                    orElse: () => banner.images.first,
                  )
                  .meta;
              return GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () async {
                  if (banner.jump != null && banner.jump!.isNotEmpty) {
                    NavigatorPushUtils.to(
                        context, banner.jump, banner.style, () {});
                  }
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16.r),
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    fit: BoxFit.cover,
                  ),
                ),
              );
            },
            onPageChanged: (index) {
              setState(() {
                _currentBannerIndex = index % _banners.length;
              });
            },
          ),
          Positioned(
            bottom: 8.h,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(_banners.length, (index) {
                return Container(
                  width: 5.w,
                  height: 5.h,
                  margin: EdgeInsets.symmetric(horizontal: 4.w),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _currentBannerIndex == index
                        ? AppTheme.primaryColor
                        : Colors.grey,
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }
}
