import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:npemployee/Utils/extensions.dart';
import 'package:npemployee/Utils/navigator_push_utils.dart';
import 'package:npemployee/common/app_info.dart';
import 'package:npemployee/common/app_theme.dart';
import 'package:npemployee/common/dialog/custom_tips_dialog.dart';
import 'package:npemployee/common/dialog/upgrade_dialog.dart';
import 'package:npemployee/constants/GlobalPreferences.dart';
import 'package:npemployee/manager/bloc_manager.dart';
import 'package:npemployee/model/im/system_notification_model.dart';
import 'package:npemployee/model/mine/me_nodel.dart';
import 'package:npemployee/network/result_data.dart';
import 'package:npemployee/page/function/function_page.dart';
import 'package:npemployee/page/home/<USER>';
import 'package:npemployee/page/im/message_list_page.dart';
import 'package:npemployee/page/mine/profile_page.dart';
import 'package:npemployee/page/study/study_page.dart';
import 'package:npemployee/provider/tab_bloc/tab_event.dart';
import 'package:npemployee/provider/tab_bloc/tab_state.dart';
import 'package:npemployee/provider/user_service_provider.dart';
import 'package:npemployee/routers/message_router.dart';
import 'package:npemployee/routers/mine_router.dart';
import 'package:npemployee/routers/navigator_utils.dart';
import 'package:package_info/package_info.dart';
import 'package:tencent_calls_uikit/tuicall_kit.dart';
import 'package:tencent_cloud_chat_push/tencent_cloud_chat_push.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:visibility_detector/visibility_detector.dart';

class Tabs extends StatefulWidget {
  final int? index;
  const Tabs({Key? key, this.index = 0}) : super(key: key);

  @override
  _TabsState createState() => _TabsState();
}

class _TabsState extends State<Tabs> with WidgetsBindingObserver {
  late int _currentIndex;
  List<Widget> _pageList = [
    HomePage(),
    StudyPage(),
    const MessageListPage(),
    const FunctionPage(),
    ProfilePage(),
  ];

  DateTime? _lastPressedAt; // 上次点击时间
  List<String> randomTitle = ['不可一日不学习', '不可一周不运动'];
  int tabOneTitleFlag = 0;
  bool _isInitialized = false;

  SystemNotificationModel? getSystemNotificationModel(String dataStr) {
    try {
      if (dataStr.isNotEmpty) {
        final customMessage = jsonDecode(dataStr);
        return SystemNotificationModel.fromJson(customMessage);
      }
      return null;
    } catch (err) {
      return null;
    }
  }

  void _handleClickNotification(
      {required String ext, String? groupID, String? userID}) async {
    if (userID != null) {
      final conversationID = TencentUtils.checkString(groupID) != null
          ? "group_$groupID"
          : "c2c_$userID";
      final targetConversationRes = await TencentImSDKPlugin.v2TIMManager
          .getConversationManager()
          .getConversation(conversationID: conversationID);

      V2TimConversation? targetConversation = targetConversationRes.data;

      if (targetConversation != null) {
        Future.delayed(const Duration(milliseconds: 100), () {
          NavigatorUtils.push(context, MessageRouter.messageDetailPage,
              arguments: {'con': targetConversation});
        });
      }
    } else {
      if (ext.isNotEmpty) {
        final systemMessage = getSystemNotificationModel(ext);
        if (systemMessage == null) {
          EasyLoading.showInfo('通知数据错误，请联系管理员!');
          return;
        }
        if (systemMessage.jump != null && systemMessage.jump!.isNotEmpty) {
          NavigatorPushUtils.to(
              context, systemMessage.jump, systemMessage.style, () {});
        }
      }
    }
  }

  void _initIM() async {
    if (GlobalPreferences().imLoginModel == null) {
      ResultData? res = await UserServiceProvider().getIMLoginInfo();
      if (res?.code == 0) {
        GlobalPreferences().imLoginModel = ImLoginModel.fromJson(res?.data);
      }
    }
    if (GlobalPreferences().imLoginModel != null) {
      TIMUIKitCore.getInstance()
          .login(
              userID: GlobalPreferences().imLoginModel!.uid,
              userSig: GlobalPreferences().imLoginModel!.sig)
          .then((v) {
        TUICallKit.instance.login(
            AppInfo().imId,
            GlobalPreferences().imLoginModel!.uid,
            GlobalPreferences().imLoginModel!.sig);
        TencentCloudChatPush().registerPush(
          sdkAppId: AppInfo().imId,
          onNotificationClicked: _handleClickNotification,
          apnsCertificateID: AppInfo().imApnsId,
        );
      });
    }
  }

  void _updateCurrentIndex(int index) {
    if (!AppInfo().registered && index != 4) {
      EasyLoading.showToast('请先注册');
      return;
    }
    if (index == 1) {
      tabOneTitleFlag = (tabOneTitleFlag + 1) % 2;
    }
    BlocManager().tabBloc.add(TabChangeEvent(index));
    setState(() {
      _currentIndex = index;
    });
  }

  void _showReviewNotPassDialog(String note) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return CustomTipsDialog(
          canPop: _currentIndex == 4,
          imagePath: 'assets/png/failed_approval.png',
          title: '抱歉\n您的审核已被驳回',
          content: '备注：${note}',
          buttonText: '重新注册',
          status: 'review_not_pass',
          onPressed: () {
            NavigatorUtils.pop(context);
            NavigatorUtils.push(
                context, MineRouter.personalInfoPage); // Close dialog
          },
        );
      },
    );
  }

  void _checkReviewStatus() {
    UserServiceProvider().getUserInfo().then((value) {
      if (value?.code == 0) {
        if (value?.data['user']['status'] == "normal") {
          GlobalPreferences().userInfo = MeModel.fromJson(value?.data);
          setState(() {});
        } else if (value?.data['user']['status'] == 'waiting_review') {
          /* showDialog(
            context: context,
            builder: (BuildContext context) {
              return CustomTipsDialog(
                imagePath: 'assets/png/commit_success.png',
                title: '信息提交成功\n请耐心等待审核',
                content: '',
                buttonText: '我知道了',
                onPressed: () {
                  Navigator.of(context).pop(); // Close dialog
                },
              );
            },
          ); */
        } else if (value?.data['user']['status'] == 'review_not_pass') {
          _showReviewNotPassDialog(value?.data['user']['note']);
        } else if (value?.data['user']['status'] == 'frozen') {
          showDialog(
            barrierDismissible: false,
            context: context,
            builder: (BuildContext context) {
              return CustomTipsDialog(
                imagePath: 'assets/png/failed_approval.png',
                title: '抱歉\n您的账号已被禁用',
                content: '请联系部门负责人\n 【蔡文姬】\n 启用账号',
                status: 'frozen',
                departments: value?.data['roles'] ?? [],
                onPressed: () {},
              );
            },
          );
        } else {}
      }
    });
  }

  void _getUpgradeInfo() async {
    ResultData? data = await UserServiceProvider().getVersionInfo();
    if (data?.code == '200') {
      if (data?.data['need_update']) {
        String storeName = '未知';
        if (Platform.isIOS) {
          storeName = 'AppStore';
        } else if (Platform.isAndroid) {
          DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
          AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
          storeName = getManufacturerName(androidInfo.manufacturer);
        }
        ResultData? storeData = await UserServiceProvider()
            .getStoreInfo(data?.data['version'], storeName);
        if (storeData?.code == '200') {
          if (Platform.isIOS) {
            if (storeData?.data['approve'] == "1") {
              _upgradeApp(data?.data, storeData?.data);
            }
          } else if (Platform.isAndroid) {
            _upgradeApp(data?.data, storeData?.data);
          }
        }
      }
    }
  }

  String getManufacturerName(String brand) {
    String paramBrand = 'XIAOMI';
    switch (brand.toUpperCase()) {
      case 'HUAWEI':
        paramBrand = "华为";
      case 'HONOR':
        paramBrand = "荣耀";
      case 'XIAOMI':
      case 'REDMI':
      case 'MI':
        paramBrand = "XIAOMI";
      case 'ONEPLUS':
      case 'OPPO':
        paramBrand = "OPPO";
      case 'VIVO':
        paramBrand = "VIVO";
      default:
        paramBrand = "XIAOMI";
    }
    return paramBrand;
  }

  void _upgradeApp(Map? data, Map? storeData) {
    showDialog(
        context: context,
        builder: (_) {
          return UpgradeDialog(
            data: data,
            storeData: storeData,
          );
        });
  }

  void _tabChangeSubscription() {
    BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.change &&
          state.page != null &&
          state.page! <= 4) {
        setState(() {
          _currentIndex = state.page!;
        });
        _checkReviewStatus();
      }
    });
  }

  void _initBrandAndCheckIsChatShow() async {
    final PackageInfo info = await PackageInfo.fromPlatform();
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    var storeName = getManufacturerName(androidInfo.manufacturer);
    ResultData? storeData =
        await UserServiceProvider().getStoreInfo(info.version, storeName);
    if (storeData?.code == '200') {
      if (storeData?.data['approve'] == "1") {
        setState(() {
          _pageList = [
            HomePage(),
            StudyPage(),
            const MessageListPage(),
            const FunctionPage(),
            ProfilePage(),
          ];
        });
      } else {
        setState(() {
          _pageList = [
            HomePage(),
            StudyPage(),
            const FunctionPage(),
            ProfilePage(),
          ];
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _currentIndex = widget.index ?? (AppInfo().registered ? 0 : 4);
    // if(Platform.isAndroid){
    //   _initBrandAndCheckIsChatShow();
    // }
    _tabChangeSubscription();
    _checkReviewStatus();
    _initIM();
    _getUpgradeInfo();
    _isInitialized = true;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      BlocManager().tabBloc.add(TabChangeEvent(_currentIndex));
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key('my-tabs-key'),
      onVisibilityChanged: (visibilityInfo) {
        var visiblePercentage = visibilityInfo.visibleFraction * 100;
        if (visibilityInfo.visibleFraction == 1.0 && _isInitialized) {
          var savedUserInfo = GlobalPreferences().userInfo;
          if (savedUserInfo != null) {
            if (savedUserInfo.user.status == "review_not_pass") {
              _updateCurrentIndex(4);
            }
          }
        }
      },
      child: PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) {
            if (didPop) return;
            if (_lastPressedAt == null ||
                DateTime.now().difference(_lastPressedAt!) >
                    const Duration(seconds: 2)) {
              _lastPressedAt = DateTime.now();
              EasyLoading.showToast('再按一次退出应用');
              return;
            }
            SystemNavigator.pop();
            return;
          },
          child: Scaffold(
            body: IndexedStack(
              index: _currentIndex,
              children: _pageList,
            ),
            bottomNavigationBar: buildBottomNavigationBar(),
          )),
    );

    /* return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) {
          if (didPop) return;
          if (_lastPressedAt == null ||
              DateTime.now().difference(_lastPressedAt!) >
                  const Duration(seconds: 2)) {
            _lastPressedAt = DateTime.now();
            EasyLoading.showToast('再按一次退出应用');
            return;
          }
          SystemNavigator.pop();
          return;
        },
        child: Scaffold(
          body: IndexedStack(
            index: _currentIndex,
            children: _pageList,
          ),
          bottomNavigationBar: buildBottomNavigationBar(),
        )); */
  }

  Widget buildBottomNavigationBar() {
    return SafeArea(
      child: Container(
        height: MediaQuery.of(context).orientation == Orientation.portrait
            ? 56
            : 72, // 根据方向调整高度
        child: Row(
          children: List.generate(_pageList.length, (index) {
            return Expanded(
              child: BottomNavBarItem(
                assetImage: _currentIndex == index
                    ? 'assets/svg/tab_${_getTabName(index)}_selected.svg'
                    : 'assets/svg/tab_${_getTabName(index)}_unselected.svg',
                title: _getTabTitle(index),
                isSelected: _currentIndex == index,
                onTap: () => _updateCurrentIndex(index),
              ),
            );
          }),
        ),
      ),
    );
  }

  String _getTabName(int index) {
    if (_pageList.length == 4) {
      switch (index) {
        case 0:
          return 'home';
        case 1:
          return 'study';
        case 2:
          return 'function';
        case 3:
          return 'mine';
        default:
          return '';
      }
    } else {
      switch (index) {
        case 0:
          return 'home';
        case 1:
          return 'study';
        case 2:
          return 'message';
        case 3:
          return 'function';
        case 4:
          return 'mine';
        default:
          return '';
      }
    }
  }

  String _getTabTitle(int index) {
    if (_pageList.length == 4) {
      switch (index) {
        case 0:
          return '首页';
        case 1:
          return randomTitle[tabOneTitleFlag];
        case 2:
          return '功能';
        case 3:
          return '我的';
        default:
          return '';
      }
    } else {
      switch (index) {
        case 0:
          return '首页';
        case 1:
          return randomTitle[tabOneTitleFlag];
        case 2:
          return '消息';
        case 3:
          return '功能';
        case 4:
          return '我的';
        default:
          return '';
      }
    }
  }
}

class BottomNavBarItem extends StatefulWidget {
  final String assetImage;
  final String title;
  final bool isSelected;
  final VoidCallback onTap;
  const BottomNavBarItem(
      {super.key,
      required this.assetImage,
      required this.title,
      required this.isSelected,
      required this.onTap});

  @override
  State<BottomNavBarItem> createState() => _BottomNavBarItemState();
}

class _BottomNavBarItemState extends State<BottomNavBarItem> {
  int mineUnread = 0;

  void _unreadListen() {
    BlocManager().tabBloc.stream.listen((state) {
      if (state.type == TabEventType.unread) {
        setState(() {
          mineUnread = state.mineUnread ?? 0;
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _unreadListen();
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTap,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Stack(
            clipBehavior: Clip.none,
            children: [
              SvgPicture.asset(
                widget.assetImage,
                width: 22,
                height: 22,
              ),
              if (widget.title == '消息')
                Positioned(
                  top: -5,
                  right: -6,
                  child: UnconstrainedBox(
                    child:
                        TIMUIKitConversationTotalUnread(width: 16, height: 16),
                  ),
                ),
              if (widget.title == '我的' && mineUnread != 0)
                Positioned(
                  top: 0,
                  right: 1,
                  child: ClipOval(
                    child: Container(width: 7, height: 7, color: Colors.red),
                  ),
                ),
            ],
          ),
          SizedBox(height: 3.h),
          Text(
            widget.title,
            style: TextStyle(
              color: widget.isSelected ? AppTheme.colorBlue : Colors.grey,
              fontSize: 10.sp,
            ).pfRegular,
          ),
        ],
      ),
    );
  }
}
