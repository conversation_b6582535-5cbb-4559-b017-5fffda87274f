<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>定位功能示例</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            padding: 16px;
            margin: 0;
            background-color: #f5f5f5;
            color: #333;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #0066cc;
            font-size: 24px;
            margin-bottom: 20px;
            text-align: center;
        }

        .section {
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 20px;
        }

        .section:last-child {
            border-bottom: none;
        }

        h2 {
            color: #333;
            font-size: 18px;
            margin-bottom: 10px;
        }

        button {
            background-color: #0066cc;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            margin: 5px 5px 5px 0;
            font-size: 14px;
            cursor: pointer;
        }

        button:disabled {
            background-color: #cccccc;
        }

        .device-list {
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 8px;
        }

        .device-item {
            padding: 12px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .device-item>div:first-child {
            flex: 1;
        }

        .device-item>div:last-child {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .device-item:last-child {
            border-bottom: none;
        }

        .device-name {
            font-weight: bold;
        }

        .device-id {
            font-size: 12px;
            color: #666;
        }

        .device-rssi {
            font-size: 12px;
            color: #999;
        }

        .connect-btn {
            background-color: #4CAF50;
        }

        .disconnect-btn {
            background-color: #F44336;
        }

        .services-btn {
            background-color: #2196F3;
        }

        .device-status {
            font-size: 12px;
            margin-top: 4px;
        }

        .log-container {
            margin-top: 20px;
            border: 1px solid #eee;
            border-radius: 4px;
            padding: 8px;
            max-height: 200px;
            overflow-y: auto;
            background-color: #f9f9f9;
            font-family: monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 4px;
            word-break: break-all;
        }

        .success {
            color: #4CAF50;
        }

        .error {
            color: #F44336;
        }

        .info {
            color: #2196F3;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>定位功能</h1>

        <div class="section">
            <h2>蓝牙状态</h2>
            <button id="checkSupported">检查蓝牙支持</button>
            <button id="getState">获取蓝牙状态</button>
            <button id="turnOn">开启蓝牙</button>
        </div>

        <div class="section">
            <h2>扫描设备</h2>
            <button id="startScan">开始扫描</button>
            <button id="stopScan">停止扫描</button>
            <div class="device-list" id="deviceList">
                <div class="device-item">点击"开始扫描"按钮开始扫描设备</div>
            </div>
        </div>

        <div class="section">
            <h2>设备连接</h2>
            <button id="getConnectedDevice">获取已连接设备</button>
            <button id="disconnect">断开连接</button>
            <button id="discoverServices">发现服务</button>
            <div id="connectedDeviceInfo"></div>
        </div>

        <div class="section">
            <h2>日志</h2>
            <div class="log-container" id="logContainer"></div>
        </div>
    </div>

    <script>
        // 全局变量
        let selectedDeviceId = null;
        let services = [];
        let deviceMap = {}; // 存储设备信息的映射，用于快速查找

        // 初始化
        document.addEventListener('DOMContentLoaded', function () {
            // 注册蓝牙回调函数
            window.bluetoothCallback = function (action, result) {
                logMessage(`收到蓝牙回调: ${action} + ${result.data}`);

                try {
                    const resultObj = result;

                    if (resultObj.code === 0) {
                        // 处理不同的回调
                        switch (action) {
                            case 'scanResultsUpdate':
                                // 实时更新扫描结果
                                updateDeviceList(resultObj.data);
                                break;
                            case 'getScanResults':
                                updateDeviceList(resultObj.data);
                                break;
                            case 'getConnectedDevice':
                                updateConnectedDeviceInfo(resultObj.data);
                                break;
                            case 'discoverServices':
                                services = resultObj.data;
                                logMessage(`发现 ${services.length} 个服务`, 'info');
                                break;
                            case 'connectToDevice':
                                logMessage(`设备连接成功`, 'success');
                                getConnectedDevice(); // 获取连接的设备信息
                                break;
                            case 'disconnectDevice':
                                logMessage(`设备断开连接成功`, 'success');
                                updateConnectedDeviceInfo(null); // 清空连接设备信息
                                break;
                            default:
                                logMessage(`${action} 成功: ${JSON.stringify(resultObj.data)}`, 'success');
                                break;
                        }
                    } else {
                        logMessage(`${action} 失败: ${resultObj.msg}`, 'error');
                    }
                } catch (e) {
                    logMessage(`解析回调结果失败: ${e}`, 'error');
                }
            };

            // 绑定按钮事件
            document.getElementById('checkSupported').addEventListener('click', checkBluetoothSupported);
            document.getElementById('getState').addEventListener('click', getBluetoothState);
            document.getElementById('turnOn').addEventListener('click', turnOnBluetooth);
            document.getElementById('startScan').addEventListener('click', startScan);
            document.getElementById('stopScan').addEventListener('click', stopScan);
            document.getElementById('getScanResults').addEventListener('click', getScanResults);
            document.getElementById('getConnectedDevice').addEventListener('click', getConnectedDevice);
            document.getElementById('disconnect').addEventListener('click', disconnectDevice);
            document.getElementById('discoverServices').addEventListener('click', discoverServices);

            logMessage('页面加载完成', 'info');
        });

        // 蓝牙功能方法
        function checkBluetoothSupported() {
            sendToNative('isBluetoothSupported');
        }

        function getBluetoothState() {
            sendToNative('getBluetoothState');
        }

        function turnOnBluetooth() {
            sendToNative('turnOnBluetooth');
        }

        function startScan() {
            // 清空设备列表并显示加载提示
            const deviceList = document.getElementById('deviceList');
            deviceList.innerHTML = '<div class="device-item">正在扫描设备...</div>';

            // 清空设备映射
            deviceMap = {};

            // 开始扫描
            sendToNative('startScan', {
                timeout: 30, // 30秒超时
                withNames: [] // 不过滤名称
            });

            logMessage('开始扫描蓝牙设备，请等待...', 'info');
        }

        function stopScan() {
            sendToNative('stopScan');
            logMessage('停止扫描蓝牙设备', 'info');
        }

        function getScanResults() {
            sendToNative('getScanResults');
        }

        function connectToDevice(deviceId) {
            selectedDeviceId = deviceId;
            sendToNative('connectToDevice', {
                deviceId: deviceId,
                autoConnect: false,
                timeout: 10 // 10秒超时
            });
        }

        function getConnectedDevice() {
            sendToNative('getConnectedDevice');
        }

        function disconnectDevice() {
            sendToNative('disconnectDevice');
        }

        function discoverServices() {
            sendToNative('discoverServices');
        }

        // 辅助方法
        function sendToNative(action, data = {}) {
            logMessage(`发送到原生: ${action}`, 'info');
            try {
                window.xtjrChannel.postMessage(JSON.stringify({
                    action: action,
                    data: data
                }));
            } catch (e) {
                logMessage(`发送消息失败: ${e}`, 'error');
            }
        }

        function updateDeviceList(devices) {
            const deviceList = document.getElementById('deviceList');

            // 保存当前设备列表，用于比较
            const currentDevices = {};
            devices.forEach(device => {
                currentDevices[device.id] = device;
                deviceMap[device.id] = device; // 更新全局设备映射
            });

            // 如果没有设备，显示提示信息
            if (devices.length === 0) {
                deviceList.innerHTML = '<div class="device-item">没有找到设备</div>';
                return;
            }

            // 更新或添加设备到列表
            devices.forEach(device => {
                // 检查设备是否已经在列表中
                let deviceElement = document.getElementById(`device-${device.id}`);

                if (!deviceElement) {
                    // 创建新的设备元素
                    deviceElement = document.createElement('div');
                    deviceElement.id = `device-${device.id}`;
                    deviceElement.className = 'device-item';
                    deviceList.appendChild(deviceElement);
                }

                // 更新设备信息
                const isConnected = device.isConnected;

                // 设备信息部分
                const deviceInfo = document.createElement('div');
                deviceInfo.innerHTML = `
                    <div class="device-name">${device.name}</div>
                    <div class="device-id">${device.id}</div>
                    <div class="device-rssi">信号强度: ${device.rssi} dBm</div>
                    <div class="device-status">状态: ${isConnected ? '<span class="success">已连接</span>' : '<span>未连接</span>'}</div>
                `;

                // 按钮部分
                const buttonContainer = document.createElement('div');

                if (isConnected) {
                    // 已连接状态，显示断开连接按钮
                    const disconnectBtn = document.createElement('button');
                    disconnectBtn.className = 'disconnect-btn';
                    disconnectBtn.textContent = '断开连接';
                    disconnectBtn.onclick = function () {
                        disconnectDevice();
                    };
                    buttonContainer.appendChild(disconnectBtn);

                    // 发现服务按钮
                    const servicesBtn = document.createElement('button');
                    servicesBtn.className = 'services-btn';
                    servicesBtn.textContent = '发现服务';
                    servicesBtn.onclick = function () {
                        discoverServices();
                    };
                    buttonContainer.appendChild(servicesBtn);
                } else {
                    // 未连接状态，显示连接按钮
                    const connectBtn = document.createElement('button');
                    connectBtn.className = 'connect-btn';
                    connectBtn.textContent = '连接';
                    connectBtn.onclick = function () {
                        connectToDevice(device.id);
                    };
                    buttonContainer.appendChild(connectBtn);
                }

                // 清空并重新添加内容
                deviceElement.innerHTML = '';
                deviceElement.appendChild(deviceInfo);
                deviceElement.appendChild(buttonContainer);
            });

            // 移除不再存在的设备
            const deviceElements = deviceList.querySelectorAll('.device-item[id^="device-"]');
            deviceElements.forEach(element => {
                const deviceId = element.id.replace('device-', '');
                if (!currentDevices[deviceId]) {
                    element.remove();
                }
            });
        }

        function updateConnectedDeviceInfo(device) {
            const connectedDeviceInfo = document.getElementById('connectedDeviceInfo');

            if (!device) {
                connectedDeviceInfo.innerHTML = '<div>没有连接的设备</div>';
                return;
            }

            connectedDeviceInfo.innerHTML = `
                <div><strong>设备名称:</strong> ${device.name}</div>
                <div><strong>设备ID:</strong> ${device.id}</div>
                <div><strong>MTU:</strong> ${device.mtu}</div>
                <div><strong>连接状态:</strong> ${device.isConnected ? '已连接' : '未连接'}</div>
            `;
        }

        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
    </script>
</body>

</html>