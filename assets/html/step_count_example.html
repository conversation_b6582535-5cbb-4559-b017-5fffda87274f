<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>步数功能示例</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
        }

        .section h2 {
            color: #007AFF;
            margin-top: 0;
            margin-bottom: 15px;
        }

        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }

        button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
            flex: 1;
            min-width: 120px;
        }

        button:hover {
            background: #0056CC;
        }

        button:active {
            background: #004499;
        }

        .input-group {
            margin-bottom: 15px;
        }

        .input-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: 500;
        }

        .input-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 14px;
            box-sizing: border-box;
        }

        .log-container {
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.info {
            color: #4CAF50;
        }

        .log-entry.error {
            color: #F44336;
        }

        .log-entry.warning {
            color: #FF9800;
        }

        .step-display {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .step-count {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .step-label {
            font-size: 16px;
            opacity: 0.9;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>步数功能示例</h1>

        <div class="step-display">
            <div class="step-count" id="stepCount">--</div>
            <div class="step-label">今日步数</div>
        </div>

        <div class="section">
            <h2>权限管理</h2>
            <div class="button-group">
                <button onclick="checkStepPermission()">检查步数权限</button>
                <button onclick="requestStepPermission()">请求步数权限</button>
                <button onclick="openAppSettings()">打开应用设置</button>
            </div>
        </div>

        <div class="section">
            <h2>步数获取</h2>
            <div class="button-group">
                <button onclick="getTodayStepCount()">获取今日步数</button>
                <button onclick="getYesterdayStepCount()">获取昨日步数</button>
            </div>
            <div class="input-group">
                <label for="dateInput">选择日期获取步数：</label>
                <input type="date" id="dateInput" onchange="getStepCountByDate()">
            </div>
        </div>

        <div class="section">
            <h2>步数监听</h2>
            <div class="button-group">
                <button onclick="startStepCountStream()">开始监听步数</button>
                <button onclick="stopStepCountStream()">停止监听步数</button>
            </div>
        </div>

        <div class="section">
            <h2>日志输出</h2>
            <div class="log-container" id="logContainer">
                <div class="log-entry info">步数功能示例页面已加载</div>
            </div>
        </div>
    </div>

    <script>
        // 向原生发送消息的通用方法
        function sendToNative(action, data = {}) {
            logMessage(`发送到原生: ${action}`, 'info');
            try {
                window.xtjrChannel.postMessage(JSON.stringify({
                    action: action,
                    data: data
                }));
            } catch (e) {
                logMessage(`发送消息失败: ${e}`, 'error');
            }
        }

        // 日志记录方法
        function logMessage(message, type = 'info') {
            const logContainer = document.getElementById('logContainer');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;

            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;

            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 步数回调处理
        window.stepCountCallback = function (action, result) {
            console.log("------------------------ ", result);
            try {
                const data = JSON.parse(result);

                logMessage(`${action} 返回: ${result}`, data.code === 0 ? 'info' : 'error');

                // 根据不同的action处理结果
                switch (action) {
                    case 'getTodayStepCount':
                    case 'getStepCountByDate':
                        if (data.code === 0) {
                            document.getElementById('stepCount').textContent = data.data.toLocaleString();
                        }
                        break;
                    case 'checkStepPermission':
                        if (data.code === 0) {
                            logMessage(`权限状态: ${data.data ? '已授权' : '未授权'}`, data.data ? 'info' : 'warning');
                        }
                        break;
                    case 'requestStepPermission':
                        if (data.code === 0) {
                            logMessage(`权限请求结果: ${data.data ? '授权成功' : '授权失败'}`, data.data ? 'info' : 'error');
                        }
                        break;
                }
            } catch (e) {
                logMessage('解析回调结果失败: ' + e.message, 'error');
            }
        };

        // 步数功能方法
        function checkStepPermission() {
            logMessage('检查步数权限...', 'info');
            sendToNative('checkStepPermission');
        }

        function requestStepPermission() {
            logMessage('请求步数权限...', 'info');
            sendToNative('requestStepPermission');
        }

        function getTodayStepCount() {
            logMessage('获取今日步数...', 'info');
            sendToNative('getTodayStepCount');
        }

        function getYesterdayStepCount() {
            const yesterday = new Date();
            yesterday.setDate(yesterday.getDate() - 1);
            logMessage('获取昨日步数...', 'info');
            sendToNative('getStepCountByDate', {
                date: yesterday.toISOString().split('T')[0]
            });
        }

        function getStepCountByDate() {
            const dateInput = document.getElementById('dateInput');
            if (dateInput.value) {
                logMessage(`获取 ${dateInput.value} 的步数...`, 'info');
                sendToNative('getStepCountByDate', {
                    date: dateInput.value
                });
            } else {
                logMessage('请选择日期', 'warning');
            }
        }

        function startStepCountStream() {
            logMessage('开始监听步数变化...', 'info');
            sendToNative('startStepCountStream');
        }

        function stopStepCountStream() {
            logMessage('停止监听步数变化...', 'info');
            sendToNative('stopStepCountStream');
        }

        function openAppSettings() {
            logMessage('打开应用设置...', 'info');
            sendToNative('openAppSettings');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function () {
            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('dateInput').value = today;

            logMessage('页面加载完成', 'info');

            // 自动获取今日步数
            setTimeout(() => {
                getTodayStepCount();
            }, 1000);
        });
    </script>
</body>

</html>