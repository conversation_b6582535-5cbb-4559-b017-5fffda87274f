PODS:
  - app_links (1.0.0):
    - FlutterMacOS
  - audio_session (0.0.1):
    - FlutterMacOS
  - connectivity_plus (0.0.1):
    - Flutter
    - FlutterMacOS
  - desktop_drop (0.0.1):
    - FlutterMacOS
  - device_info_plus (0.0.1):
    - FlutterMacOS
  - fc_native_video_thumbnail (0.0.1):
    - Flutter
    - FlutterMacOS
  - file_selector_macos (0.0.1):
    - FlutterMacOS
  - flutter_blue_plus_darwin (0.0.2):
    - Flutter
    - FlutterMacOS
  - flutter_image_compress_macos (1.0.0):
    - FlutterMacOS
  - flutter_inappwebview_macos (0.0.1):
    - FlutterMacOS
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - flutter_timezone (0.1.0):
    - FlutterMacOS
  - flutter_tts (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - geolocator_apple (1.2.0):
    - FlutterMacOS
  - HydraAsync (2.0.6)
  - just_audio (0.0.1):
    - FlutterMacOS
  - mobile_scanner (5.2.3):
    - FlutterMacOS
  - open_file_mac (0.0.1):
    - FlutterMacOS
  - OrderedSet (6.0.3)
  - package_info (0.0.1):
    - FlutterMacOS
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - pasteboard (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - photo_manager (2.0.0):
    - Flutter
    - FlutterMacOS
  - share_plus (0.0.1):
    - FlutterMacOS
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - syncfusion_pdfviewer_macos (0.0.1):
    - FlutterMacOS
  - tencent_cloud_chat_sdk (8.0.0):
    - FlutterMacOS
    - HydraAsync
    - TXIMSDK_Plus_Mac (= 8.1.6122)
  - TXIMSDK_Plus_Mac (8.1.6122)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - app_links (from `Flutter/ephemeral/.symlinks/plugins/app_links/macos`)
  - audio_session (from `Flutter/ephemeral/.symlinks/plugins/audio_session/macos`)
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin`)
  - desktop_drop (from `Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos`)
  - device_info_plus (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos`)
  - fc_native_video_thumbnail (from `Flutter/ephemeral/.symlinks/plugins/fc_native_video_thumbnail/darwin`)
  - file_selector_macos (from `Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos`)
  - flutter_blue_plus_darwin (from `Flutter/ephemeral/.symlinks/plugins/flutter_blue_plus_darwin/darwin`)
  - flutter_image_compress_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_image_compress_macos/macos`)
  - flutter_inappwebview_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - flutter_timezone (from `Flutter/ephemeral/.symlinks/plugins/flutter_timezone/macos`)
  - flutter_tts (from `Flutter/ephemeral/.symlinks/plugins/flutter_tts/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - geolocator_apple (from `Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos`)
  - just_audio (from `Flutter/ephemeral/.symlinks/plugins/just_audio/macos`)
  - mobile_scanner (from `Flutter/ephemeral/.symlinks/plugins/mobile_scanner/macos`)
  - open_file_mac (from `Flutter/ephemeral/.symlinks/plugins/open_file_mac/macos`)
  - package_info (from `Flutter/ephemeral/.symlinks/plugins/package_info/macos`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - pasteboard (from `Flutter/ephemeral/.symlinks/plugins/pasteboard/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - photo_manager (from `Flutter/ephemeral/.symlinks/plugins/photo_manager/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - shared_preferences_foundation (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin`)
  - syncfusion_pdfviewer_macos (from `Flutter/ephemeral/.symlinks/plugins/syncfusion_pdfviewer_macos/macos`)
  - tencent_cloud_chat_sdk (from `Flutter/ephemeral/.symlinks/plugins/tencent_cloud_chat_sdk/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)
  - webview_flutter_wkwebview (from `Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - HydraAsync
    - OrderedSet
    - TXIMSDK_Plus_Mac

EXTERNAL SOURCES:
  app_links:
    :path: Flutter/ephemeral/.symlinks/plugins/app_links/macos
  audio_session:
    :path: Flutter/ephemeral/.symlinks/plugins/audio_session/macos
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/darwin
  desktop_drop:
    :path: Flutter/ephemeral/.symlinks/plugins/desktop_drop/macos
  device_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus/macos
  fc_native_video_thumbnail:
    :path: Flutter/ephemeral/.symlinks/plugins/fc_native_video_thumbnail/darwin
  file_selector_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/file_selector_macos/macos
  flutter_blue_plus_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_blue_plus_darwin/darwin
  flutter_image_compress_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_image_compress_macos/macos
  flutter_inappwebview_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_inappwebview_macos/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  flutter_timezone:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_timezone/macos
  flutter_tts:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_tts/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  geolocator_apple:
    :path: Flutter/ephemeral/.symlinks/plugins/geolocator_apple/macos
  just_audio:
    :path: Flutter/ephemeral/.symlinks/plugins/just_audio/macos
  mobile_scanner:
    :path: Flutter/ephemeral/.symlinks/plugins/mobile_scanner/macos
  open_file_mac:
    :path: Flutter/ephemeral/.symlinks/plugins/open_file_mac/macos
  package_info:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info/macos
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  pasteboard:
    :path: Flutter/ephemeral/.symlinks/plugins/pasteboard/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  photo_manager:
    :path: Flutter/ephemeral/.symlinks/plugins/photo_manager/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  shared_preferences_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_foundation/darwin
  sqflite_darwin:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite_darwin/darwin
  syncfusion_pdfviewer_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/syncfusion_pdfviewer_macos/macos
  tencent_cloud_chat_sdk:
    :path: Flutter/ephemeral/.symlinks/plugins/tencent_cloud_chat_sdk/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos
  webview_flutter_wkwebview:
    :path: Flutter/ephemeral/.symlinks/plugins/webview_flutter_wkwebview/darwin

SPEC CHECKSUMS:
  app_links: 9028728e32c83a0831d9db8cf91c526d16cc5468
  audio_session: 48ab6500f7a5e7c64363e206565a5dfe5a0c1441
  connectivity_plus: 2256d3e20624a7749ed21653aafe291a46446fee
  desktop_drop: e0b672a7d84c0a6cbc378595e82cdb15f2970a43
  device_info_plus: a56e6e74dbbd2bb92f2da12c64ddd4f67a749041
  fc_native_video_thumbnail: b511cec81fad66be9b28dd54b9adb39d40fcd6cc
  file_selector_macos: 6280b52b459ae6c590af5d78fc35c7267a3c4b31
  flutter_blue_plus_darwin: 20a08bfeaa0f7804d524858d3d8744bcc1b6dbc3
  flutter_image_compress_macos: e68daf54bb4bf2144c580fd4d151c949cbf492f0
  flutter_inappwebview_macos: c2d68649f9f8f1831bfcd98d73fd6256366d9d1d
  flutter_local_notifications: 13862b132e32eb858dea558a86d45d08daeacfe7
  flutter_timezone: bed4b9a391e5727a612e722d7f518882c5da205d
  flutter_tts: ae915565cc6948444b513acc8ee021993281e027
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  geolocator_apple: ccfb79d5250de3a295f5093cd03e76aa8836a416
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  just_audio: eb8b016ac4493159ab24db4f7215e55303b39a84
  mobile_scanner: bd1e7cd9b67b442f4d903747f4778e040513f860
  open_file_mac: 01874b6d6a2c1485ac9b126d7105b99102dea2cf
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info: e551c138e2e968cbbe8acf7f4afb9a5c672a1b08
  package_info_plus: f0052d280d17aa382b932f399edf32507174e870
  pasteboard: 278d8100149f940fb795d6b3a74f0720c890ecb7
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  photo_manager: d2fbcc0f2d82458700ee6256a15018210a81d413
  share_plus: 3c787998077d6b31e839225a282e9e27edf99274
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  syncfusion_pdfviewer_macos: 94d2eafcb3475d32309d4ba282ef02dc75fd682f
  tencent_cloud_chat_sdk: 776572bdd33e4b1eaa1a7b1be090d4be8d1ff57c
  TXIMSDK_Plus_Mac: 47425447a5cb201d76190ad992366c7eaf6979f7
  url_launcher_macos: 0fba8ddabfc33ce0a9afe7c5fef5aab3d8d2d673
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: 21ddc249ac4b8d018838dbdabd65c5976c308497
  webview_flutter_wkwebview: 44d4dee7d7056d5ad185d25b38404436d56c547c

PODFILE CHECKSUM: 9ebaf0ce3d369aaa26a9ea0e159195ed94724cf3

COCOAPODS: 1.16.2
