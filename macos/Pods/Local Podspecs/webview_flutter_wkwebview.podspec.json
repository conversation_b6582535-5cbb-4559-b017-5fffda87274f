{"name": "webview_flutter_wkwebview", "version": "0.0.1", "summary": "A WebView Plugin for Flutter.", "description": "A Flutter plugin that provides a WebView widget.\nDownloaded by pub (not CocoaPods).", "homepage": "https://github.com/flutter/packages", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Dev Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/packages/tree/main/packages/webview_flutter/webview_flutter_wkwebview"}, "documentation_url": "https://pub.dev/packages/webview_flutter", "source_files": "webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/**/*.{h,m}", "public_header_files": "webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/**/*.h", "module_map": "webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/include/FlutterWebView.modulemap", "ios": {"dependencies": {"Flutter": []}}, "osx": {"dependencies": {"FlutterMacOS": []}}, "platforms": {"ios": "12.0", "osx": "10.14"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "resource_bundles": {"webview_flutter_wkwebview_privacy": ["webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy"]}}