{"name": "share_plus", "version": "0.0.1", "summary": "No-op spec for share_plus_macos to avoid build issues", "description": "No-op spec for share_plus_macos to avoid build issues.\nhttps://github.com/flutter/flutter/issues/46618", "homepage": "https://github.com/fluttercommunity/plus_plugins/tree/main/packages/share_plus", "license": {"file": "../LICENSE"}, "authors": {"Flutter Community": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "public_header_files": "Classes/**/*.h", "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.11"}}