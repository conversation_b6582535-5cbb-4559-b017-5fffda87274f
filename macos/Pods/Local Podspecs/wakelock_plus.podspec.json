{"name": "wakelock_plus", "version": "0.0.1", "summary": "Plugin that allows you to keep the device screen awake, i.e. prevent the screen from sleeping on Android, iOS, macOS, Windows, and web.", "description": "Plugin that allows you to keep the device screen awake, i.e. prevent the screen from sleeping on Android, iOS, macOS, Windows, and web.", "homepage": "https://github.com/fluttercommunity/wakelock_plus", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Team": "<EMAIL>"}, "source": {"http": "https://github.com/fluttercommunity/wakelock_plus/tree/main/packages/wakelock_plus_macos"}, "source_files": "Classes/**/*", "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.11"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "swift_versions": "5.0", "swift_version": "5.0"}