{"name": "flutter_inappwebview_macos", "version": "0.0.1", "summary": "A new Flutter plugin project.", "description": "A new Flutter plugin project.", "homepage": "http://example.com", "license": {"file": "../LICENSE"}, "authors": {"Your Company": "<EMAIL>"}, "source": {"path": "."}, "source_files": "Classes/**/*", "resources": "Storyboards/**/*.storyboard", "public_header_files": "Classes/**/*.h", "dependencies": {"FlutterMacOS": [], "OrderedSet": ["~>6.0.3"]}, "resource_bundles": {"flutter_inappwebview_macos_privacy": ["Resources/PrivacyInfo.xcprivacy"]}, "platforms": {"osx": "10.14"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "swift_versions": "5.0", "swift_version": "5.0"}