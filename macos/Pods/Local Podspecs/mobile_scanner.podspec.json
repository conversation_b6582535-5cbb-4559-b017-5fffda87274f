{"name": "mobile_scanner", "version": "5.2.3", "summary": "An universal scanner for Flutter based on MLKit.", "description": "An universal scanner for Flutter based on MLKit.", "homepage": "https://github.com/julians<PERSON>kker/mobile_scanner", "license": {"file": "../LICENSE"}, "authors": {"Julian Steenbakker": "juli<PERSON><PERSON><PERSON>@outlook.com"}, "source": {"path": "."}, "source_files": "mobile_scanner/Sources/mobile_scanner/**/*.swift", "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.14"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "swift_versions": "5.0", "swift_version": "5.0"}