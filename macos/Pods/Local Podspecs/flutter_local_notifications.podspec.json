{"name": "flutter_local_notifications", "version": "0.0.1", "summary": "Flutter plugin for displaying local notifications.", "description": "Flutter plugin for displaying local notifications.", "homepage": "https://github.com/MaikuB/flutter_local_notifications/tree/master/flutter_local_notifications", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Michael Bui": "micha<PERSON>@dexterx.dev"}, "source": {"path": "."}, "source_files": "Classes/**/*", "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.14"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "resource_bundles": {"flutter_local_notifications_privacy": ["Resources/PrivacyInfo.xcprivacy"]}, "swift_versions": "5.0", "swift_version": "5.0"}