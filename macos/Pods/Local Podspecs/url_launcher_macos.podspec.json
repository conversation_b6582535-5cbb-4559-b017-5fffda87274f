{"name": "url_launcher_macos", "version": "0.0.1", "summary": "Flutter macos plugin for launching a URL.", "description": "A macOS implementation of the url_launcher plugin.", "homepage": "https://github.com/flutter/packages/tree/main/packages/url_launcher/url_launcher_macos", "license": {"type": "BSD", "file": "../LICENSE"}, "authors": {"Flutter Team": "<EMAIL>"}, "source": {"http": "https://github.com/flutter/packages/tree/main/packages/url_launcher/url_launcher_macos"}, "source_files": "url_launcher_macos/Sources/url_launcher_macos/**/*.swift", "resource_bundles": {"url_launcher_macos_privacy": ["url_launcher_macos/Sources/url_launcher_macos/Resources/PrivacyInfo.xcprivacy"]}, "dependencies": {"FlutterMacOS": []}, "platforms": {"osx": "10.14"}, "pod_target_xcconfig": {"DEFINES_MODULE": "YES"}, "swift_versions": "5.0", "swift_version": "5.0"}