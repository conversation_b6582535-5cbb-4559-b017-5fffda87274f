<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>SchemeUserState</key>
	<dict>
		<key>FlutterMacOS.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>HydraAsync.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>OrderedSet-OrderedSet_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>OrderedSet.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-Runner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>Pods-RunnerTests.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>TXIMSDK_Plus_Mac.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>app_links-app_links_macos_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>app_links.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>audio_session.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>connectivity_plus-connectivity_plus_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>connectivity_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>desktop_drop.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>device_info_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>fc_native_video_thumbnail.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>file_selector_macos-file_selector_macos_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>file_selector_macos.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_blue_plus_darwin.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_image_compress_macos.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_inappwebview_macos-flutter_inappwebview_macos_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_inappwebview_macos.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_local_notifications-flutter_local_notifications_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_local_notifications.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_timezone.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>flutter_tts.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>geolocator_apple.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>just_audio.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>mobile_scanner.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>open_file_mac.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>package_info.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>package_info_plus-package_info_plus_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>package_info_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>pasteboard.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation-path_provider_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>path_provider_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>photo_manager-photo_manager_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>photo_manager.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>share_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation-shared_preferences_foundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>shared_preferences_foundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin-sqflite_darwin_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>sqflite_darwin.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>syncfusion_pdfviewer_macos.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>tencent_cloud_chat_sdk.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_macos-url_launcher_macos_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>url_launcher_macos.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>video_player_avfoundation-video_player_avfoundation_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>video_player_avfoundation.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>wakelock_plus.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>webview_flutter_wkwebview-webview_flutter_wkwebview_privacy.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
		<key>webview_flutter_wkwebview.xcscheme</key>
		<dict>
			<key>isShown</key>
			<false/>
		</dict>
	</dict>
	<key>SuppressBuildableAutocreation</key>
	<dict/>
</dict>
</plist>
