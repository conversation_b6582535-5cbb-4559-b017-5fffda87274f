CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/HydraAsync" "${PODS_CONFIGURATION_BUILD_DIR}/OrderedSet" "${PODS_CONFIGURATION_BUILD_DIR}/app_links" "${PODS_CONFIGURATION_BUILD_DIR}/audio_session" "${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus" "${PODS_CONFIGURATION_BUILD_DIR}/desktop_drop" "${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus" "${PODS_CONFIGURATION_BUILD_DIR}/fc_native_video_thumbnail" "${PODS_CONFIGURATION_BUILD_DIR}/file_selector_macos" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_blue_plus_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_image_compress_macos" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_inappwebview_macos" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_timezone" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_tts" "${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple" "${PODS_CONFIGURATION_BUILD_DIR}/just_audio" "${PODS_CONFIGURATION_BUILD_DIR}/mobile_scanner" "${PODS_CONFIGURATION_BUILD_DIR}/open_file_mac" "${PODS_CONFIGURATION_BUILD_DIR}/package_info" "${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus" "${PODS_CONFIGURATION_BUILD_DIR}/pasteboard" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/photo_manager" "${PODS_CONFIGURATION_BUILD_DIR}/share_plus" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin" "${PODS_CONFIGURATION_BUILD_DIR}/syncfusion_pdfviewer_macos" "${PODS_CONFIGURATION_BUILD_DIR}/tencent_cloud_chat_sdk" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_macos" "${PODS_CONFIGURATION_BUILD_DIR}/video_player_avfoundation" "${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus" "${PODS_CONFIGURATION_BUILD_DIR}/webview_flutter_wkwebview" "${PODS_ROOT}/TXIMSDK_Plus_Mac"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/HydraAsync/Hydra.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/OrderedSet/OrderedSet.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/app_links/app_links.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/audio_session/audio_session.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/connectivity_plus/connectivity_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/desktop_drop/desktop_drop.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/device_info_plus/device_info_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/fc_native_video_thumbnail/fc_native_video_thumbnail.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/file_selector_macos/file_selector_macos.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_blue_plus_darwin/flutter_blue_plus_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_image_compress_macos/flutter_image_compress_macos.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_inappwebview_macos/flutter_inappwebview_macos.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_local_notifications/flutter_local_notifications.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_timezone/flutter_timezone.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/flutter_tts/flutter_tts.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/geolocator_apple/geolocator_apple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/just_audio/just_audio.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/mobile_scanner/mobile_scanner.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/open_file_mac/open_file_mac.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/package_info/package_info.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/package_info_plus/package_info_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/pasteboard/pasteboard.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/path_provider_foundation/path_provider_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/photo_manager/photo_manager.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/share_plus/share_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/shared_preferences_foundation/shared_preferences_foundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/sqflite_darwin/sqflite_darwin.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/syncfusion_pdfviewer_macos/syncfusion_pdfviewer_macos.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/tencent_cloud_chat_sdk/tencent_cloud_chat_sdk.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/url_launcher_macos/url_launcher_macos.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/video_player_avfoundation/video_player_avfoundation.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/wakelock_plus/wakelock_plus.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/webview_flutter_wkwebview/webview_flutter_wkwebview.framework/Headers" ${PODS_ROOT}/TXIMSDK_Plus_Mac/ImSDKForMac_Plus.framework/Versions/A/Headers/, ${PODS_ROOT}/TXIMSDK_Plus_Mac/ImSDKForMac_Plus.framework/cpluscplus/include/
LD_RUNPATH_SEARCH_PATHS = $(inherited) /usr/lib/swift
LIBRARY_SEARCH_PATHS = $(inherited) $(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)/ $(SDKROOT)/usr/lib/swift
OTHER_LDFLAGS = $(inherited) -framework "CoreBluetooth" -framework "Foundation" -framework "Hydra" -framework "ImSDKForMac_Plus" -framework "OrderedSet" -framework "PhotosUI" -framework "app_links" -framework "audio_session" -framework "connectivity_plus" -framework "desktop_drop" -framework "device_info_plus" -framework "fc_native_video_thumbnail" -framework "file_selector_macos" -framework "flutter_blue_plus_darwin" -framework "flutter_image_compress_macos" -framework "flutter_inappwebview_macos" -framework "flutter_local_notifications" -framework "flutter_timezone" -framework "flutter_tts" -framework "geolocator_apple" -framework "just_audio" -framework "mobile_scanner" -framework "open_file_mac" -framework "package_info" -framework "package_info_plus" -framework "pasteboard" -framework "path_provider_foundation" -framework "photo_manager" -framework "share_plus" -framework "shared_preferences_foundation" -framework "sqflite_darwin" -framework "syncfusion_pdfviewer_macos" -framework "tencent_cloud_chat_sdk" -framework "url_launcher_macos" -framework "video_player_avfoundation" -framework "wakelock_plus" -framework "webview_flutter_wkwebview"
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_PODFILE_DIR_PATH = ${SRCROOT}/.
PODS_ROOT = ${SRCROOT}/Pods
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
