import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:npemployee/provider/header_color_provider.dart';

/// HeaderColorProvider测试
void main() {
  group('HeaderColorProvider 测试', () {
    late HeaderColorProvider provider;

    setUp(() {
      provider = HeaderColorProvider();
    });

    test('初始状态应该没有自定义颜色', () {
      expect(provider.hasCustomColor, isFalse);
      expect(provider.headerColor, isNull);
      expect(provider.textColor, isNull);
      expect(provider.iconBrightness, isNull);
    });

    test('设置头部颜色应该正确计算相关属性', () {
      const testColor = Colors.red;
      
      provider.setHeaderColor(testColor);
      
      expect(provider.hasCustomColor, isTrue);
      expect(provider.headerColor, equals(testColor));
      expect(provider.textColor, isNotNull);
      expect(provider.iconBrightness, isNotNull);
    });

    test('重置颜色应该清除所有自定义设置', () {
      // 先设置一个颜色
      provider.setHeaderColor(Colors.blue);
      expect(provider.hasCustomColor, isTrue);
      
      // 然后重置
      provider.resetColor();
      
      expect(provider.hasCustomColor, isFalse);
      expect(provider.headerColor, isNull);
      expect(provider.textColor, isNull);
      expect(provider.iconBrightness, isNull);
    });

    test('亮色背景应该使用深色文字和图标', () {
      const lightColor = Colors.white;
      
      provider.setHeaderColor(lightColor);
      
      expect(provider.textColor, equals(Colors.black87));
      expect(provider.iconBrightness, equals(Brightness.dark));
    });

    test('暗色背景应该使用浅色文字和图标', () {
      const darkColor = Colors.black;
      
      provider.setHeaderColor(darkColor);
      
      expect(provider.textColor, equals(Colors.white));
      expect(provider.iconBrightness, equals(Brightness.light));
    });
  });
}
