import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:npemployee/provider/home_color_bloc/home_color_bloc.dart';
import 'package:npemployee/provider/home_color_bloc/home_color_event.dart';
import 'package:npemployee/provider/home_color_bloc/home_color_state.dart';

/// Home页面颜色BLoC测试
void main() {
  group('HomeColorBloc 测试', () {
    late HomeColorBloc homeColorBloc;

    setUp(() {
      homeColorBloc = HomeColorBloc();
    });

    tearDown(() {
      homeColorBloc.dispose();
    });

    test('初始状态应该是默认状态', () {
      expect(homeColorBloc.state, equals(HomeColorState.defaultState));
    });

    test('发送ChangeHomeColorEvent应该更新状态', () async {
      const testStatusBarColor = Colors.red;
      const testTextColor = Colors.white;
      const testBrightness = Brightness.light;

      homeColorBloc.add(ChangeHomeColorEvent(
        statusBarColor: testStatusBarColor,
        textColor: testTextColor,
        iconBrightness: testBrightness,
      ));

      // 等待状态更新
      await Future.delayed(const Duration(milliseconds: 100));

      expect(homeColorBloc.state.statusBarColor, equals(testStatusBarColor));
      expect(homeColorBloc.state.textColor, equals(testTextColor));
      expect(homeColorBloc.state.iconBrightness, equals(testBrightness));
      expect(homeColorBloc.state.isDefault, isFalse);
    });

    test('发送ResetHomeColorEvent应该重置为默认状态', () async {
      // 先设置一个非默认颜色
      homeColorBloc.add(ChangeHomeColorEvent(
        statusBarColor: Colors.blue,
        textColor: Colors.white,
        iconBrightness: Brightness.light,
      ));

      await Future.delayed(const Duration(milliseconds: 100));

      // 然后重置
      homeColorBloc.add(ResetHomeColorEvent());

      await Future.delayed(const Duration(milliseconds: 100));

      expect(homeColorBloc.state, equals(HomeColorState.defaultState));
    });

    test('状态流应该发出正确的状态变化', () async {
      const testStatusBarColor = Colors.green;
      const testTextColor = Colors.white;

      // 监听状态流
      final states = <HomeColorState>[];
      final subscription = homeColorBloc.stream.listen((state) {
        states.add(state);
      });

      // 发送颜色变更事件
      homeColorBloc.add(ChangeHomeColorEvent(
        statusBarColor: testStatusBarColor,
        textColor: testTextColor,
      ));

      await Future.delayed(const Duration(milliseconds: 100));

      // 发送重置事件
      homeColorBloc.add(ResetHomeColorEvent());

      await Future.delayed(const Duration(milliseconds: 100));

      subscription.cancel();

      // 验证状态变化序列
      expect(states.length, equals(2));
      expect(states[0].statusBarColor, equals(testStatusBarColor));
      expect(states[0].textColor, equals(testTextColor));
      expect(states[0].isDefault, isFalse);
      expect(states[1], equals(HomeColorState.defaultState));
    });

    test('HomeColorState copyWith 方法应该正确工作', () {
      const originalState = HomeColorState(
        statusBarColor: Colors.red,
        textColor: Colors.white,
        iconBrightness: Brightness.light,
        isDefault: false,
      );

      final newState = originalState.copyWith(
        statusBarColor: Colors.blue,
        isDefault: true,
      );

      expect(newState.statusBarColor, equals(Colors.blue));
      expect(newState.textColor, equals(Colors.white)); // 保持不变
      expect(newState.iconBrightness, equals(Brightness.light)); // 保持不变
      expect(newState.isDefault, isTrue);
    });

    test('状态栏颜色应该根据_currentTopIndex正确应用', () {
      // 这个测试用于验证状态栏颜色逻辑：
      // _currentTopIndex == 0 时使用 _currentHomeColorState.statusBarColor
      // _currentTopIndex != 0 时使用 Colors.transparent

      const testState = HomeColorState(
        statusBarColor: Colors.red,
        textColor: Colors.white,
        iconBrightness: Brightness.light,
        isDefault: false,
      );

      // 模拟 _currentTopIndex == 0 的情况
      expect(testState.statusBarColor, equals(Colors.red));
      expect(testState.isDefault, isFalse);

      // 模拟 _currentTopIndex != 0 的情况应该使用透明色
      // 这个逻辑在HomePage的build方法中实现
      expect(Colors.transparent, equals(Colors.transparent));
    });
  });
}
